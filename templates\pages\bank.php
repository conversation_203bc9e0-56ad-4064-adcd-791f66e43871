<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

// ค่าคงที่สำหรับการจัดการธนาคาร
define('BAM_SUPPORTED_BANK_TYPES', array('f789', 's888', 'jnp', 'kaw', 'czo'));
define('BAM_BANK_TABLE_ID', 'display_bank');
define('BAM_BANK_SEARCH_INPUT_ID', 'custom-search');

// ฟิลด์ที่ต้องเข้ารหัส
define('BAM_ENCRYPTED_FIELDS', array('account_name', 'bank_number', 'phone_number', 'national_id'));

// คอลัมน์ตาราง
define('BAM_TABLE_COLUMNS', array(
    'ลำดับ', 'ธนาคาร', 'ชื่อบัญชี', 'เลขบัญชีธนาคาร',
    'รหัส ATM', 'รหัส App', 'รหัสตัวแทน', 'วันที่ใช้งาน',
    'หมายเหตุ', 'สถานะ', 'การกระทำ'
));
/**
 * ตรวจสอบความถูกต้องของ bank type
 * @param string $bank_type ประเภทธนาคาร
 * @return bool true ถ้า bank type ถูกต้อง
 */
function bam_is_valid_bank_type($bank_type)
{
    return in_array($bank_type, BAM_SUPPORTED_BANK_TYPES, true);
}

/**
 * สร้าง permission key สำหรับการเข้าถึง
 * @param string $bank_type ประเภทธนาคาร
 * @param string $action การกระทำ (access, add)
 * @return string permission key
 */
function bam_get_permission_key($bank_type, $action = 'access')
{
    return "can_{$action}_{$bank_type}";
}

/**
 * ตรวจสอบสิทธิ์การเข้าถึง
 * @param string $permission_key คีย์สิทธิ์
 * @return bool true ถ้ามีสิทธิ์
 */
function bam_has_permission($permission_key)
{
    if (!isset($_SESSION)) {
        session_start();
    }

    return isset($_SESSION['bam_permission'][$permission_key]) &&
           intval($_SESSION['bam_permission'][$permission_key]) === 1;
}
/**
 * แสดงตารางข้อมูลธนาคาร
 * @param string $bank_type ประเภทธนาคาร
 * @return string HTML ของตาราง
 */
function bam_render_bank_table($bank_type)
{
    // ตรวจสอบความถูกต้องของ bank type
    if (!bam_is_valid_bank_type($bank_type)) {
        return '<div class="alert alert-danger">ประเภทธนาคารไม่ถูกต้อง</div>';
    }

    ob_start();
    ?>
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4 p-3">
                <?php echo bam_render_table_header($bank_type); ?>
                <?php echo bam_render_table_content($bank_type); ?>
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * แสดงส่วนหัวของตาราง
 * @param string $bank_type ประเภทธนาคาร
 * @return string HTML ของส่วนหัวตาราง
 */
function bam_render_table_header($bank_type)
{
    ob_start();
    ?>
    <div class="row">
        <div class="col-md-10 col-5">
            <?php echo bam_render_add_button($bank_type); ?>
        </div>
        <div class="col-md-2 col-7">
            <?php echo bam_render_search_input(); ?>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * แสดงปุ่มเพิ่มบัญชี
 * @param string $bank_type ประเภทธนาคาร
 * @return string HTML ของปุ่มเพิ่ม
 */
function bam_render_add_button($bank_type)
{
    $add_permission_key = bam_get_permission_key($bank_type, 'access_add_bank');

    if (!bam_has_permission($add_permission_key)) {
        return '<!-- ไม่มีสิทธิ์เพิ่มบัญชี -->';
    }

    $add_url = "/add-bank-{$bank_type}/";

    return sprintf(
        '<a href="%s" onclick="navigatePage(event, this.href);" class="btn btn-primary shadow-sm">
            <i class="bi bi-plus-lg"></i> เพิ่มบัญชี
        </a>',
        esc_url($add_url)
    );
}

/**
 * แสดงช่องค้นหา
 * @return string HTML ของช่องค้นหา
 */
function bam_render_search_input()
{
    return sprintf(
        '<input type="text" id="%s" class="form-control" placeholder="ค้นหา">',
        esc_attr(BAM_BANK_SEARCH_INPUT_ID)
    );
}

/**
 * แสดงเนื้อหาตาราง
 * @param string $bank_type ประเภทธนาคาร
 * @return string HTML ของเนื้อหาตาราง
 */
function bam_render_table_content($bank_type)
{
    ob_start();
    ?>
    <div class="col-md-12 mt-2">
        <div class="table-responsive">
            <table id="<?php echo esc_attr(BAM_BANK_TABLE_ID); ?>"
                   class="table table-bordered table-striped table-hover border rounded"
                   data-bank-type="<?php echo esc_attr($bank_type); ?>">
                <?php echo bam_render_table_head(); ?>
                <tbody></tbody> <!-- JS เติม -->
            </table>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * แสดงหัวตาราง
 * @return string HTML ของหัวตาราง
 */
function bam_render_table_head()
{
    $header_html = '<thead><tr>';
    foreach (BAM_TABLE_COLUMNS as $column) {
        $header_html .= '<th>' . esc_html($column) . '</th>';
    }
    $header_html .= '</tr></thead>';

    return $header_html;
}

/**
 * ฟังก์ชันสำหรับแสดงตารางธนาคาร (backward compatibility)
 * @param string $bank_type ประเภทธนาคาร
 * @return string HTML ของตาราง
 */
function display_bank($bank_type)
{
    return bam_render_bank_table($bank_type);
}

/**
 * โหลดข้อมูลธนาคารจากฐานข้อมูล
 * @param string $bank_type ประเภทธนาคาร
 * @return array ข้อมูลธนาคารที่ถอดรหัสแล้ว
 */
function bam_load_bank_data_from_db($bank_type)
{
    // ตรวจสอบความถูกต้องของ bank type
    if (!bam_is_valid_bank_type($bank_type)) {
        return array();
    }

    global $wpdb;
    $table = $wpdb->prefix . 'bank_account';

    // ใช้ prepared statement เพื่อความปลอดภัย
    $results = $wpdb->get_results(
        $wpdb->prepare("SELECT * FROM $table WHERE bank_type = %s ORDER BY created_date DESC", $bank_type),
        'ARRAY_A'
    );

    if (!$results) {
        return array();
    }

    // ถอดรหัสข้อมูลสำคัญ
    return bam_decrypt_sensitive_data($results);
}

/**
 * ถอดรหัสข้อมูลสำคัญในผลลัพธ์
 * @param array $results ข้อมูลที่ดึงจากฐานข้อมูล
 * @return array ข้อมูลที่ถอดรหัสแล้ว
 */
function bam_decrypt_sensitive_data($results)
{
    foreach ($results as &$row) {
        foreach (BAM_ENCRYPTED_FIELDS as $field) {
            if (!empty($row[$field])) {
                $decrypted = bam_decrypt($row[$field]);
                $row[$field] = $decrypted !== null ? $decrypted : $row[$field];
            }
        }
    }

    return $results;
}

/**
 * สร้าง permission map สำหรับ bank types
 * @return array permission map
 */
function bam_get_permission_map()
{
    $permission_map = array();
    foreach (BAM_SUPPORTED_BANK_TYPES as $type) {
        $permission_map[$type] = bam_get_permission_key($type);
    }
    return $permission_map;
}

/**
 * ตรวจสอบและโหลดข้อมูลสำหรับ AJAX request
 */
function bam_handle_ajax_load_data()
{
    // โหลด dependencies
    bam_load_dependencies();

    // ตรวจสอบและ sanitize input
    $bank_type = bam_sanitize_bank_type_input();

    // ตรวจสอบสิทธิ์
    bam_verify_bank_access_permission($bank_type);

    // โหลดและส่งข้อมูล
    $data = bam_load_bank_data_from_db($bank_type);
    wp_send_json_success($data);
}

/**
 * โหลด dependencies ที่จำเป็น
 */
function bam_load_dependencies()
{
    require_once get_template_directory() . '/includes/permission-handler.php';
    require_once get_template_directory() . '/includes/encryption.php';
}

/**
 * ตรวจสอบและทำความสะอาด bank type input
 * @return string bank type ที่ทำความสะอาดแล้ว
 */
function bam_sanitize_bank_type_input()
{
    $type = sanitize_text_field(isset($_GET['type']) ? $_GET['type'] : '');

    if (empty($type)) {
        wp_send_json_error(array('message' => 'Missing bank type'));
    }

    if (!bam_is_valid_bank_type($type)) {
        wp_send_json_error(array('message' => 'Invalid bank type'));
    }

    return $type;
}

/**
 * ตรวจสอบสิทธิ์การเข้าถึงธนาคาร
 * @param string $bank_type ประเภทธนาคาร
 */
function bam_verify_bank_access_permission($bank_type)
{
    $permission_key = bam_get_permission_key($bank_type);
    verify_ajax_permission($permission_key);
}

/**
 * สร้าง shortcode สำหรับแต่ละประเภทธนาคาร
 */
foreach (BAM_SUPPORTED_BANK_TYPES as $type) {
    add_shortcode("display_bank_{$type}", function () use ($type) {
        require_once get_template_directory() . '/includes/permission-handler.php';

        $permission_key = bam_get_permission_key($type);
        check_permission($permission_key); // 🔒 ถ้าไม่มีสิทธิ์ → wp_redirect

        return display_bank($type); // ✅ ปลอดภัยแล้ว
    });
}

/**
 * ฟังก์ชันสำหรับโหลดข้อมูลธนาคาร (AJAX handler)
 */
function bam_load_bank_data()
{
    bam_handle_ajax_load_data();
}
add_action('wp_ajax_nopriv_load_bank_data', 'bam_load_bank_data');
?>