<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

function display_bank($bank_type)
{
    ob_start();
?>
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4 p-3">
                <div class="row">
                    <div class="col-md-10 col-5">
                        <?php
                        $permission_key = "can_access_add_bank_" . $bank_type;
                        $can_add_bank = isset($_SESSION['bam_permission'][$permission_key]) &&
                                        intval($_SESSION['bam_permission'][$permission_key]) === 1;
                        ?>

                        <?php if ($can_add_bank): ?>
                        <a href="/add-bank-<?php echo $bank_type; ?>/" onclick="navigatePage(event, this.href);" class="btn btn-primary shadow-sm">
                            <i class="bi bi-plus-lg"></i> เพิ่มบัญชี
                        </a>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-2 col-7">
                        <input type="text" id="custom-search" class="form-control" placeholder="ค้นหา">
                    </div>
                    <div class="col-md-12 mt-2">
                        <div class="table-responsive">
                            <table id="display_bank" class="table table-bordered table-striped table-hover border rounded" data-bank-type="<?php echo esc_attr($bank_type); ?>">
                                <thead>
                                    <tr>
                                        <th>ลำดับ</th>
                                        <th>ธนาคาร</th>
                                        <th>ชื่อบัญชี</th>
                                        <th>เลขบัญชีธนาคาร</th>
                                        <th>รหัส ATM</th>
                                        <th>รหัส App</th>
                                        <th>รหัสตัวแทน</th>
                                        <th>วันที่ใช้งาน</th>
                                        <th>หมายเหตุ</th>
                                        <th>สถานะ</th>
                                        <th>การกระทำ</th>
                                    </tr>
                                </thead>
                                <tbody></tbody> <!-- JS เติม -->
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php
    return ob_get_clean(); // คืนค่า HTML ที่เก็บไว้ในบัฟเฟอร์
}

// ✅ เพิ่ม shortcode สำหรับ bank_type แต่ละประเภทโดยใช้ loop
$bank_types = ['f789', 's888', 'jnp', 'kaw', 'czo'];

foreach ($bank_types as $type) {
    add_shortcode("display_bank_{$type}", function () use ($type) {
        require_once get_template_directory() . '/includes/permission-handler.php';

        $permissionKey = "can_access_{$type}";
        check_permission($permissionKey); // 🔒 ถ้าไม่มีสิทธิ์ → wp_redirect

        return display_bank($type); // ✅ ปลอดภัยแล้ว
    });
}

// โหลดข้อมูล
function bam_load_bank_data()
{
    require_once get_template_directory() . '/includes/permission-handler.php';
    require_once get_template_directory() . '/includes/encryption.php';

    $type = sanitize_text_field($_GET['type'] ?? '');
    if (!$type) {
        wp_send_json_error(['message' => 'Missing bank type']);
    }

    // ✅ ตรวจสิทธิ์แบบ dynamic ตาม bank_type
    $permissionMap = [
        'f789' => 'can_access_f789',
        's888' => 'can_access_s888',
        'jnp'  => 'can_access_jnp',
        'kaw'  => 'can_access_kaw',
        'czo'  => 'can_access_czo',
    ];

    if (!isset($permissionMap[$type])) {
        wp_send_json_error(['message' => 'Invalid bank type']);
    }

    // ✅ ตรวจเฉพาะสิทธิ์ของ bank ที่ขอมา
    verify_ajax_permission($permissionMap[$type]);

    global $wpdb;
    $table = $wpdb->prefix . 'bank_account';
    $query = $wpdb->prepare("SELECT * FROM $table WHERE bank_type = %s", $type);
    $results = $wpdb->get_results($query, ARRAY_A);

    // 🔓 ถอดรหัสข้อมูลสำคัญก่อนส่งให้ frontend
    foreach ($results as &$row) {
        if (!empty($row['account_name'])) {
            $row['account_name'] = bam_decrypt($row['account_name']);
        }

        if (!empty($row['bank_number'])) {
            $row['bank_number'] = bam_decrypt($row['bank_number']);
        }

        if (!empty($row['phone_number'])) {
            $row['phone_number'] = bam_decrypt($row['phone_number']);
        }

        if (!empty($row['national_id'])) {
            $row['national_id'] = bam_decrypt($row['national_id']);
        }
    }

    wp_send_json_success($results);
}
add_action('wp_ajax_nopriv_load_bank_data', 'bam_load_bank_data');
?>