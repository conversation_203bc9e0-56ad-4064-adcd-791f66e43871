label {
    margin-bottom: 0.3rem;
    color: var(--bs-dark);
    font-size: 0.9rem;
}

.upload-box {
    height: 140px;
    border: 3px dashed var(--bs-indigo-200);

    .upload-label {
        background-color: var(--bs-gray-500);
        cursor: pointer;
        overflow: hidden;

        .upload-icon {
            font-size: 3.5rem;
        }
    }
}

@media screen and (max-width: 767px) {

    .upload-box {
        height: 187px;
        margin-bottom: 15px;
    }
}

/* เบลอเฉพาะเมื่อมีรูปและ hover */
#nid-preview-wrapper.has-image:hover #nid-preview {
    filter: blur(2px);
}

/* แสดง overlay เฉพาะเมื่อมีรูปและ hover */
#nid-preview-wrapper.has-image:hover .upload-overlay {
    opacity: 1;
}

#nid-preview-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

#nid-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: filter 0.3s ease;
    z-index: 1;
    display: none; /* ซ่อนไว้ตอนแรก */
}

.upload-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: var(--bs-white);
    opacity: 0;
    z-index: 2;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.upload-overlay .upload-icon {
    font-size: 2rem;
}

.upload-text {
    font-size: 0.9rem;
}