/**
 * BAM User Role Management JavaScript Module
 * จัดการตาราง User Roles และการโต้ตอบกับผู้ใช้
 */
(function($) {
    'use strict';

    /**
     * คลาสสำหรับจัดการ User Role Table
     */
    class BAMUserRoleManager {
        constructor() {
            // ค่าคงที่สำหรับการจัดการ user roles
            this.config = {
                tableSelector: '#display_userrole',
                ajaxAction: 'load_userrole_data',
                moduleName: 'UserRole',
                columns: [
                    { key: 'index', label: 'ลำดับ' },
                    { key: 'role_name', label: 'ชื่อบทบาท' },
                    { key: 'actions', label: 'การกระทำ' }
                ]
            };

            this.init();
        }

        /**
         * เริ่มต้นการทำงานของโมดูล
         */
        init() {
            console.log(`BAM ${this.config.moduleName} Manager initialized.`);

            if (this.isTableExists()) {
                this.loadTableData();
            }
        }

        /**
         * ตรวจสอบว่าตารางมีอยู่หรือไม่
         * @returns {boolean}
         */
        isTableExists() {
            return $(this.config.tableSelector).length > 0;
        }

        /**
         * โหลดข้อมูลตาราง
         */
        loadTableData() {
            const $table = $(this.config.tableSelector);

            if (!$table.length) {
                console.warn(`Table ${this.config.tableSelector} not found`);
                return;
            }

            this.performAjaxRequest($table);
        }

        /**
         * ทำการร้องขอ AJAX
         * @param {jQuery} $table - jQuery object ของตาราง
         */
        performAjaxRequest($table) {
            const ajaxConfig = this.getAjaxConfig();

            $.ajax({
                url: ajaxConfig.url,
                method: ajaxConfig.method,
                data: ajaxConfig.data,
                success: (response) => this.handleAjaxSuccess(response, $table),
                error: (xhr, status, error) => this.handleAjaxError(xhr, status, error)
            });
        }

        /**
         * สร้างการตั้งค่า AJAX
         * @returns {Object} การตั้งค่า AJAX
         */
        getAjaxConfig() {
            return {
                url: bamAjaxConfig.ajaxurl,
                method: 'GET',
                data: {
                    action: this.config.ajaxAction,
                    nonce: bamAjaxConfig.nonce
                }
            };
        }

        /**
         * จัดการผลลัพธ์ AJAX เมื่อสำเร็จ
         * @param {Object} response - ผลลัพธ์จากเซิร์ฟเวอร์
         * @param {jQuery} $table - jQuery object ของตาราง
         */
        handleAjaxSuccess(response, $table) {
            if (this.isValidResponse(response)) {
                const tableRows = this.generateTableRows(response.data);
                this.updateTableContent($table, tableRows);
                this.initializeDataTable($table);
            } else {
                this.showWarning(`โหลดข้อมูล${this.config.moduleName}ไม่สำเร็จ`);
            }
        }

        /**
         * ตรวจสอบความถูกต้องของ response
         * @param {Object} response - ผลลัพธ์จากเซิร์ฟเวอร์
         * @returns {boolean}
         */
        isValidResponse(response) {
            return response && response.success && Array.isArray(response.data);
        }

        /**
         * สร้างแถวตาราง
         * @param {Array} data - ข้อมูลสำหรับสร้างแถว
         * @returns {string} HTML ของแถวตาราง
         */
        generateTableRows(data) {
            return data.map((item, index) => this.createTableRow(item, index)).join('');
        }

        /**
         * สร้างแถวตารางเดี่ยว
         * @param {Object} item - ข้อมูลแถว
         * @param {number} index - ลำดับแถว
         * @returns {string} HTML ของแถวเดี่ยว
         */
        createTableRow(item, index) {
            const safeItem = this.sanitizeData(item);

            return `
                <tr>
                    <td>${index + 1}</td>
                    <td>${safeItem.role_name}</td>
                    <td class="text-center">
                        ${this.createActionDropdown(safeItem.id)}
                    </td>
                </tr>
            `;
        }

        /**
         * ทำความสะอาดข้อมูลเพื่อป้องกัน XSS
         * @param {Object} item - ข้อมูลที่ต้องทำความสะอาด
         * @returns {Object} ข้อมูลที่ทำความสะอาดแล้ว
         */
        sanitizeData(item) {
            return {
                id: parseInt(item.id) || 0,
                role_name: this.escapeHtml(item.role_name || ''),
                created_at: this.escapeHtml(item.created_at || '')
            };
        }

        /**
         * Escape HTML เพื่อป้องกัน XSS
         * @param {string} text - ข้อความที่ต้อง escape
         * @returns {string} ข้อความที่ escape แล้ว
         */
        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        /**
         * สร้าง dropdown สำหรับการกระทำ
         * @param {number} itemId - ID ของรายการ
         * @returns {string} HTML ของ dropdown
         */
        createActionDropdown(itemId) {
            const actions = [
                { action: 'view', icon: 'bi-info-circle-fill', label: 'ดูข้อมูล', class: 'text-white' },
                { action: 'edit', icon: 'bi-gear-fill', label: 'แก้ไข', class: 'text-white' },
                { action: 'delete', icon: 'bi-trash-fill', label: 'ลบ', class: 'text-danger' }
            ];

            const actionItems = actions.map(action =>
                `<li><a class="dropdown-item ${action.class}" href="#" data-action="${action.action}" data-id="${itemId}">
                    <i class="bi ${action.icon}"></i> ${action.label}
                </a></li>`
            ).join('');

            return `
                <div class="dropdown">
                    <button class="btn btn-primary" type="button" data-bs-toggle="dropdown">
                        ดำเนินการ <i class="bi bi-chevron-down"></i>
                    </button>
                    <ul class="dropdown-menu">
                        ${actionItems}
                    </ul>
                </div>
            `;
        }

        /**
         * อัปเดตเนื้อหาตาราง
         * @param {jQuery} $table - jQuery object ของตาราง
         * @param {string} rows - HTML ของแถวตาราง
         */
        updateTableContent($table, rows) {
            $table.find('tbody').html(rows);
        }

        /**
         * เริ่มต้น DataTable
         * @param {jQuery} $table - jQuery object ของตาราง
         */
        initializeDataTable($table) {
            if (typeof initializeDataTable === 'function') {
                initializeDataTable($table);
            } else {
                console.warn('initializeDataTable function not found');
            }
        }

        /**
         * จัดการข้อผิดพลาด AJAX
         * @param {Object} xhr - XMLHttpRequest object
         * @param {string} status - สถานะข้อผิดพลาด
         * @param {string} error - ข้อความข้อผิดพลาด
         */
        handleAjaxError(xhr, status, error) {
            console.error(`เกิดข้อผิดพลาดในการโหลดข้อมูล${this.config.moduleName}จาก AJAX:`, {
                status: status,
                error: error,
                response: xhr.responseText
            });

            this.showError(`ไม่สามารถโหลดข้อมูล${this.config.moduleName}ได้ กรุณาลองใหม่อีกครั้ง`);
        }

        /**
         * แสดงข้อความเตือน
         * @param {string} message - ข้อความเตือน
         */
        showWarning(message) {
            console.warn(message);
            // สามารถเพิ่มการแสดง UI notification ได้ที่นี่
        }

        /**
         * แสดงข้อความข้อผิดพลาด
         * @param {string} message - ข้อความข้อผิดพลาด
         */
        showError(message) {
            console.error(message);
            // สามารถเพิ่มการแสดง UI error notification ได้ที่นี่
        }
    }

    /**
     * เริ่มต้นโมดูลเมื่อ DOM พร้อม
     */
    $(function() {
        // สร้าง instance ของ BAMUserRoleManager
        const userRoleManager = new BAMUserRoleManager();

        // เก็บ reference ไว้ใน window object สำหรับการเข้าถึงจากภายนอก
        window.bamUserRoleManager = userRoleManager;

        // รักษาความเข้ากันได้กับโค้ดเดิม
        window.initializeUserroleTables = function() {
            userRoleManager.loadTableData();
        };
    });

})(jQuery);