/* begin::sidebar */
.sidebar-mini {

    .app-sidebar {
        padding: 0px 13px;

        &.bg-body-secondary {
            background-color: var(--bs-indigo-900) !important;
        }

        .badge {
            background-color: var(--bs-red);
            margin: 1px 20px !important;
        }
    }

    .c-sidebar-brand {

        .c-brand-image {
            width: auto;
            max-height: 33px;
            line-height: 0.8;
        }

        .c-brand-text {
            opacity: 1;
            transition: opacity 0.3s ease;
        }
    }

    .c-sidebar-wrapper {

        .nav-link {
            color: var(--bs-gray-300);
        }

        .nav-link.active {
            background-color: var(--bs-indigo-200);
        }

        .nav-treeview {

            .nav-link.active {
                color: var(--bs-dark);
                background-color: var(--bs-white);
            }
        }
    }

    &.sidebar-collapse {

        .c-brand-text {
            opacity: 0;
            max-width: 0;
        }

        .c-sidebar-wrapper {

            .nav-link {

                &.active {
                    max-width: 48px;
                }

                &.btn-logout.bg-danger {
                    max-width: 48px;
                }
            }
        }

        .app-sidebar:hover {

            .c-brand-text {
                display: inline;
                max-width: inherit;
                margin-left: 0.5rem;
                opacity: 1;
                transition: opacity 0.3s ease;
            }

            .nav-link p {
                display: inline;
                max-width: inherit;
                opacity: 1;
                transition: opacity 0.3s ease;
            }

            .nav-link.active {
                min-width: 222px;
            }

            .btn-logout.bg-danger {
                min-width: 222px;
            }
        }
    }
}
/* end::sidebar */