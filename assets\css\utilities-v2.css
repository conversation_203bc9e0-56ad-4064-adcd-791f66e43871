.bg-gray-200 {
    background-color: var(--bs-gray-200);
}

.text-gray-400 {
    color: var(--bs-gray-400);
}

.text-gray-500 {
    color: var(--bs-gray-500);
}

.card {
    border-radius: 15px;
    box-shadow: none;
    border: none;
}

.bg-blur {
    backdrop-filter: blur(20px);
    background-color: rgba(255, 255, 255, 0.5); /* ให้ดูโปร่งใสและเบลอซ้อน */
    border-radius: 12px;
    box-shadow: 0 0 10px rgba(0,0,0,0.3);
}

.text-shadow {
    text-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
}

.version-line-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;

    .line {
        flex-grow: 1;
        height: 1px;
        background-color: var(--bs-gray-500);
    }
}