<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

function get_bam_version() {
    return 'v0.10.1 (beta)';
}

add_action('init', function () {
    require_once get_template_directory() . '/templates/pages/login.php';
    require_once get_template_directory() . '/templates/pages/bank.php';
    require_once get_template_directory() . '/templates/pages/userrole.php';
    require_once get_template_directory() . '/templates/pages/assistance.php';
    require_once get_template_directory() . '/templates/pages/add-bank.php';
    require_once get_template_directory() . '/templates/pages/add-assistance.php';
    require_once get_template_directory() . '/includes/permission-handler.php';
});

function bam_enqueue_scripts_and_styles() {
    $base_path = get_template_directory();
    $base_uri  = get_template_directory_uri();

    // Jquery
    wp_enqueue_script('jquery');

    wp_enqueue_script('adminlte', $base_uri . '/assets/js/plugins/adminlte.js',
        ['jquery'], '4.0.0', true);

    wp_enqueue_script('bootstrap', $base_uri . '/assets/js/plugins/bootstrap.bundle.min.js',
        ['jquery'], '5.1.3', true);

    wp_enqueue_script('core-js', $base_uri . '/assets/js/core.js',
        ['jquery', 'adminlte', 'form-handler-js', 'sweetalert2'], time(), true);

    wp_localize_script('core-js', 'bamPermission', get_bam_permission_for_js());

    wp_enqueue_script('sweetalert2', $base_uri . '/assets/js/plugins/sweetalert2.min.js',
        ['jquery'], '11.21.0', true);

    // begin::Data Tables
    wp_enqueue_script('datatable', $base_uri . '/assets/js/data-tables/jquery.dataTables.min.js',
        ['jquery'], null, true);
    wp_enqueue_script('datatable-bs4', $base_uri . '/assets/js/data-tables/dataTables.bootstrap4.min.js',
        ['datatable'], null, true);
    wp_enqueue_script('datatable-responsive', $base_uri . '/assets/js/data-tables/dataTables.responsive.min.js',
        ['datatable'], null, true);
    wp_enqueue_script('datatable-responsive-bs4', $base_uri . '/assets/js/data-tables/responsive.bootstrap4.min.js',
        ['datatable'], null, true);
    wp_enqueue_script('datatable-helper', $base_uri . '/assets/js/data-tables/datatable-helper.js',
        ['core-js'], filemtime($base_path . '/assets/js/data-tables/datatable-helper.js'), true);
    // end::Data Tables

    // begin::Custom scripts
    wp_enqueue_script('bank-js', $base_uri . '/assets/js/pages/bank.js',
        ['datatable-helper'], time(), true);
    wp_enqueue_script('userrole-js', $base_uri . '/assets/js/pages/userrole.js',
        ['datatable-helper'], time(), true);
    wp_enqueue_script('assistance-js', $base_uri . '/assets/js/pages/assistance.js',
        ['datatable-helper'], time(), true);
    wp_enqueue_script('login', $base_uri . '/assets/js/pages/login.js',
        ['core-js'], time(), true);
    wp_enqueue_script('sweetalert-handler-js', $base_uri . '/assets/js/sweetalert-handler.js',
        ['sweetalert2'], time(), true);
    wp_enqueue_script('form-handler-js', $base_uri . '/assets/js/form-handler.js',
        ['adminlte'], time(), true);
    // end::Custom scripts

    // สำหรับ form-handler.js
    wp_localize_script('form-handler-js', 'bamAjaxConfig', [
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce'   => wp_create_nonce('bam_nonce_action')
    ]);

    // สำหรับ bank.js
    wp_localize_script('bank-js', 'bamAjaxConfig', [
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce'   => wp_create_nonce('bam_nonce_action')
    ]);

    // สำหรับ assistance.js
    wp_localize_script('assistance-js', 'bamAjaxConfig', [
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce'   => wp_create_nonce('bam_nonce_action')
    ]);

    // สำหรับ userrole.js
    wp_localize_script('userrole-js', 'bamAjaxConfig', [
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce'   => wp_create_nonce('bam_nonce_action')
    ]);

    $css_uri = $base_uri . '/assets/css';

    wp_enqueue_style('adminlte', $css_uri . '/plugins/adminlte.css',
        [], filemtime($base_path . '/assets/css/plugins/adminlte.css'));

    wp_enqueue_style('sweetalert2', $css_uri . '/plugins/sweetalert2.min.css',
        ['adminlte'], '11.21.0');

    // begin::Data Tables
    wp_enqueue_style('bam-table', $css_uri . '/data-tables/data-table.css',
        ['adminlte'], filemtime($base_path . '/assets/css/data-tables/data-table.css'));
    wp_enqueue_style('buttons-bootstrap4', $css_uri . '/data-tables/buttons.bootstrap4.min.css',
        ['adminlte'], null);
    wp_enqueue_style('dataTables-bootstrap4', $css_uri . '/data-tables/dataTables.bootstrap4.min.css',
        ['adminlte'], null);
    wp_enqueue_style('responsive-bootstrap4', $css_uri . '/data-tables/responsive.bootstrap4.min.css',
        ['adminlte'], null);
    // end::Data Tables

    // begin::Custom styles
    wp_enqueue_style('my-style', $css_uri . '/my-style.css',
        ['adminlte'], filemtime($base_path . '/assets/css/my-style.css'));
    wp_enqueue_style('page-login', $css_uri . '/page-login.css',
        ['adminlte'], filemtime($base_path . '/assets/css/page-login.css'));
    wp_enqueue_style('bam-layout', $css_uri . '/layout.css',
        ['adminlte'], filemtime($base_path . '/assets/css/layout.css'));
    wp_enqueue_style('bam-form', $css_uri . '/form.css',
        ['adminlte'], filemtime($base_path . '/assets/css/form.css'));
    wp_enqueue_style('bam-utilities', $css_uri . '/utilities-v2.css',
        ['adminlte'], filemtime($base_path . '/assets/css/utilities-v2.css'));
    // end::Custom styles
}
add_action('wp_enqueue_scripts', 'bam_enqueue_scripts_and_styles');

add_action('admin_post_nopriv_bam_logout', function () {
    check_admin_referer('bam_logout_nonce');

    session_start();
    unset($_SESSION['bam_user'], $_SESSION['bam_user_id']);
    session_destroy();
    wp_redirect(home_url('/login'));
    exit;
});
?>