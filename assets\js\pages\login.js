jQuery(function ($) {
    console.log("Login.js is ready.");

    $('#bam-login-form').on('submit', function (e) {
        e.preventDefault();
        const formData = $(this).serialize();
        const $alert = $('.error-alert');

        $alert.html(''); // เคลียร์ข้อความเดิมก่อน

        $.post('/wp-admin/admin-post.php', formData)
            .done(function (res) {
                const $alert = $('.error-alert');
                const url = res?.data?.redirect || null;

                if (res.success && url) {
                    window.location.href = url;
                } else {
                    const message = res?.data?.message || res.message || 'เข้าสู่ระบบไม่สำเร็จ';

                    // ✅ ตรวจว่ามีตัวเลข countdown ไหม
                    const countdownMatch = message.match(/อีก\s*(\d+)\s*วินาที/);
                    if (countdownMatch) {
                        let remaining = parseInt(countdownMatch[1]);
                        const baseMessage = message.replace(/อีก\s*\d+\s*วินาที/, 'อีก <span id="countdown-sec">' + remaining + '</span> วินาที');

                        $alert.html(`
                            <div class="alert alert-danger" role="alert">
                                ${baseMessage}
                            </div>
                        `);

                        // ✅ เริ่มนับถอยหลัง
                        const interval = setInterval(() => {
                            remaining--;
                            $('#countdown-sec').text(remaining);

                            if (remaining <= 0) {
                                clearInterval(interval);
                                $alert.html(''); // ลบ alert หลังหมดเวลา หรือจะเปลี่ยนข้อความก็ได้
                            }
                        }, 1000);
                    } else {
                        // แสดงข้อความทั่วไป
                        $alert.html(`
                            <div class="alert alert-danger" role="alert">
                                ${message}
                            </div>
                        `);
                    }
                }
            });
    });
});