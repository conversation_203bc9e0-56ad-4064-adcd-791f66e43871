/**
 * BAM Login Manager JavaScript Module
 * จัดการฟอร์มเข้าสู่ระบบและการแสดงผลข้อความ
 */
(function($) {
    'use strict';

    /**
     * คลาสสำหรับจัดการ Login Form
     */
    class BAMLoginManager {
        constructor() {
            // ค่าคงที่สำหรับการจัดการ login
            this.config = {
                formSelector: '#bam-login-form',
                alertSelector: '.error-alert',
                countdownSelector: '#countdown-sec',
                submitUrl: '/wp-admin/admin-post.php',
                countdownInterval: 1000,
                defaultErrorMessage: 'เข้าสู่ระบบไม่สำเร็จ'
            };

            // Regular expressions
            this.patterns = {
                countdown: /อีก\s*(\d+)\s*วินาที/,
                countdownReplace: /อีก\s*\d+\s*วินาที/
            };

            // State management
            this.state = {
                isSubmitting: false,
                countdownTimer: null
            };

            this.init();
        }

        /**
         * เริ่มต้นการทำงานของโมดูล
         */
        init() {
            console.log('BAM Login Manager initialized.');
            this.bindEvents();
        }

        /**
         * ผูก event handlers
         */
        bindEvents() {
            $(this.config.formSelector).on('submit', (e) => this.handleFormSubmit(e));
        }

        /**
         * จัดการการ submit ฟอร์ม
         * @param {Event} e - Event object
         */
        async handleFormSubmit(e) {
            e.preventDefault();

            if (this.state.isSubmitting) {
                return;
            }

            try {
                this.state.isSubmitting = true;
                this.clearAlert();

                const formData = this.getFormData(e.target);
                const response = await this.submitForm(formData);

                this.handleResponse(response);
            } catch (error) {
                this.handleError(error);
            } finally {
                this.state.isSubmitting = false;
            }
        }

        /**
         * ดึงข้อมูลจากฟอร์ม
         * @param {HTMLFormElement} form - Form element
         * @returns {string} Serialized form data
         */
        getFormData(form) {
            return $(form).serialize();
        }

        /**
         * ส่งข้อมูลฟอร์มผ่าน AJAX
         * @param {string} formData - Serialized form data
         * @returns {Promise} Promise object
         */
        submitForm(formData) {
            return $.post(this.config.submitUrl, formData);
        }

        /**
         * จัดการ response จากเซิร์ฟเวอร์
         * @param {Object} response - Server response
         */
        handleResponse(response) {
            const redirectUrl = this.extractRedirectUrl(response);

            if (response.success && redirectUrl) {
                this.handleSuccessfulLogin(redirectUrl);
            } else {
                const message = this.extractErrorMessage(response);
                this.handleLoginError(message);
            }
        }

        /**
         * ดึง redirect URL จาก response
         * @param {Object} response - Server response
         * @returns {string|null} Redirect URL หรือ null
         */
        extractRedirectUrl(response) {
            return response?.data?.redirect || null;
        }

        /**
         * ดึงข้อความ error จาก response
         * @param {Object} response - Server response
         * @returns {string} Error message
         */
        extractErrorMessage(response) {
            return response?.data?.message ||
                   response?.message ||
                   this.config.defaultErrorMessage;
        }

        /**
         * จัดการเมื่อเข้าสู่ระบบสำเร็จ
         * @param {string} redirectUrl - URL สำหรับ redirect
         */
        handleSuccessfulLogin(redirectUrl) {
            // ใช้ replace แทน href เพื่อไม่ให้กลับมาหน้า login ได้
            window.location.replace(redirectUrl);
        }

        /**
         * จัดการข้อผิดพลาดในการเข้าสู่ระบบ
         * @param {string} message - ข้อความ error
         */
        handleLoginError(message) {
            const sanitizedMessage = this.sanitizeMessage(message);

            if (this.isCountdownMessage(sanitizedMessage)) {
                this.handleCountdownMessage(sanitizedMessage);
            } else {
                this.showErrorMessage(sanitizedMessage);
            }
        }

        /**
         * ทำความสะอาดข้อความเพื่อป้องกัน XSS
         * @param {string} message - ข้อความที่ต้องทำความสะอาด
         * @returns {string} ข้อความที่ทำความสะอาดแล้ว
         */
        sanitizeMessage(message) {
            if (typeof message !== 'string') {
                return this.config.defaultErrorMessage;
            }

            // สร้าง temporary element เพื่อ escape HTML
            const div = document.createElement('div');
            div.textContent = message;
            return div.innerHTML;
        }

        /**
         * ตรวจสอบว่าเป็นข้อความ countdown หรือไม่
         * @param {string} message - ข้อความที่ต้องตรวจสอบ
         * @returns {boolean} true ถ้าเป็นข้อความ countdown
         */
        isCountdownMessage(message) {
            return this.patterns.countdown.test(message);
        }

        /**
         * จัดการข้อความที่มี countdown
         * @param {string} message - ข้อความที่มี countdown
         */
        handleCountdownMessage(message) {
            const countdownMatch = message.match(this.patterns.countdown);
            if (!countdownMatch) return;

            const initialTime = parseInt(countdownMatch[1], 10);
            if (isNaN(initialTime) || initialTime <= 0) return;

            this.startCountdown(message, initialTime);
        }

        /**
         * เริ่มต้น countdown timer
         * @param {string} message - ข้อความต้นฉบับ
         * @param {number} initialTime - เวลาเริ่มต้น (วินาที)
         */
        startCountdown(message, initialTime) {
            // ล้าง timer เดิมถ้ามี
            this.clearCountdownTimer();

            let remaining = initialTime;
            const baseMessage = this.createCountdownMessage(message, remaining);

            this.showErrorMessage(baseMessage);

            // เริ่ม countdown
            this.state.countdownTimer = setInterval(() => {
                remaining--;
                this.updateCountdownDisplay(remaining);

                if (remaining <= 0) {
                    this.clearCountdownTimer();
                    this.clearAlert();
                }
            }, this.config.countdownInterval);
        }

        /**
         * สร้างข้อความ countdown พร้อม span element
         * @param {string} message - ข้อความต้นฉบับ
         * @param {number} seconds - จำนวนวินาที
         * @returns {string} ข้อความที่มี countdown span
         */
        createCountdownMessage(message, seconds) {
            return message.replace(
                this.patterns.countdownReplace,
                `อีก <span id="countdown-sec">${seconds}</span> วินาที`
            );
        }

        /**
         * อัปเดตการแสดงผล countdown
         * @param {number} seconds - จำนวนวินาทีที่เหลือ
         */
        updateCountdownDisplay(seconds) {
            $(this.config.countdownSelector).text(seconds);
        }

        /**
         * ล้าง countdown timer
         */
        clearCountdownTimer() {
            if (this.state.countdownTimer) {
                clearInterval(this.state.countdownTimer);
                this.state.countdownTimer = null;
            }
        }

        /**
         * แสดงข้อความ error
         * @param {string} message - ข้อความ error
         */
        showErrorMessage(message) {
            const alertHtml = this.createAlertHtml(message, 'danger');
            $(this.config.alertSelector).html(alertHtml);
        }

        /**
         * สร้าง HTML สำหรับ alert
         * @param {string} message - ข้อความ
         * @param {string} type - ประเภท alert (danger, success, warning, info)
         * @returns {string} HTML ของ alert
         */
        createAlertHtml(message, type = 'danger') {
            return `
                <div class="alert alert-${type}" role="alert">
                    ${message}
                </div>
            `;
        }

        /**
         * ล้างข้อความ alert
         */
        clearAlert() {
            $(this.config.alertSelector).html('');
        }

        /**
         * จัดการข้อผิดพลาดทั่วไป
         * @param {Error} error - Error object
         */
        handleError(error) {
            console.error('Login error:', error);

            const errorMessage = error.message || this.config.defaultErrorMessage;
            this.showErrorMessage(this.sanitizeMessage(errorMessage));
        }

        /**
         * ทำลาย instance และล้าง resources
         */
        destroy() {
            this.clearCountdownTimer();
            $(this.config.formSelector).off('submit');
        }
    }

    /**
     * เริ่มต้นโมดูลเมื่อ DOM พร้อม
     */
    $(function() {
        // ตรวจสอบว่ามีฟอร์ม login หรือไม่
        if ($(BAMLoginManager.prototype.config?.formSelector || '#bam-login-form').length > 0) {
            // สร้าง instance ของ BAMLoginManager
            const loginManager = new BAMLoginManager();

            // เก็บ reference ไว้ใน window object สำหรับการเข้าถึงจากภายนอก
            window.bamLoginManager = loginManager;

            // ล้าง resources เมื่อออกจากหน้า
            $(window).on('beforeunload', () => {
                if (window.bamLoginManager) {
                    window.bamLoginManager.destroy();
                }
            });
        }
    });

})(jQuery);