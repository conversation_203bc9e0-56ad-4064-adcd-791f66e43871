<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$path = trim(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH), '/');

// เฉพาะ path ที่ต้องการให้ login เท่านั้น
$protected_paths = ['', 'dashboard', 'f789', 'userrole', 'assistance'];

foreach ($protected_paths as $protected) {
    if (strpos($path, $protected) === 0 && !isset($_SESSION['bam_user_id'])) {
        wp_redirect(home_url('/login'));
        exit;
    }
}
include_once get_template_directory() . '/layout/header.php';
include_once get_template_directory() . '/layout/navbar.php';
include_once get_template_directory() . '/layout/sidebar.php';
include_once get_template_directory() . '/layout/main-content.php';
include_once get_template_directory() . '/layout/footer.php';
// include_once 'control-sidebar.php'; ตัวอย่างการ include ไฟล์ที่ไม่ใช่ php
?>