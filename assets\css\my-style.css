body {
    background: var(--bs-white);
    font-family: 'Kani<PERSON>', sans-serif;
}

.dropdown-menu {
    --bs-dropdown-bg: var(--bs-indigo-900);
    --bs-dropdown-link-hover-bg: var(--bs-gray-800);
    
    & i {
        margin-right: 0.3rem;
    }
}

.btn-primary {
    --bs-btn-bg: var(--bs-indigo-200);
    --bs-btn-active-bg: var(--bs-indigo-300);
    --bs-btn-hover-bg: var(--bs-indigo-300);
}

.btn-logout {
    background: linear-gradient(145deg, #f94545, #a01b1a);
}

::placeholder {
    color: var(--bs-gray-500) !important;
}

/* สำหรับ Chrome, Safari, Edge */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}