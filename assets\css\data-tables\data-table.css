.dataTables_wrapper {

    @media screen and (min-width: 768px) {

        .c-info {
            position: absolute;
        }
    }

    .table-responsive {
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    
        &::-webkit-scrollbar {
            height: 12px;
        }
    
        &::-webkit-scrollbar-thumb {
            background-color: #999;
            border-radius: 6px;
        }
    
        &::-webkit-scrollbar-track {
            background-color: #eee;
            border-radius: 6px;
        }
    }

    .table {
        margin-top: 15px !important;

        &.rounded {
            border-radius: 10px !important;
        }

        & th {
            white-space: nowrap;
            background-color: #4f4f63 !important;
            padding-top: 5px !important;
            padding-bottom: 5px !important;
            color: #fff !important;
            border: 1px solid #4f4f63 !important;
        }

        & td {
            white-space: nowrap;
            vertical-align: middle;
        }

        td:last-child {
            border-right: none;
        }

        th:first-child {
            border-top-left-radius: 10px;
        }

        th:last-child {
            border-top-right-radius: 10px;
        }

        tr:last-child td:first-child {
            border-bottom-left-radius: 10px;
        }

        tr:last-child td:last-child {
            border-bottom-right-radius: 10px;
        }
    }

    .page-item.active {

        .page-link {
            background-color: #4f46e5 !important;
        }
    }

    @media screen and (max-width: 767px) {
    
        .table {
    
            & th {
                border: none !important;
            }
    
            td:nth-child(3),td:nth-child(4) {
                border-right: none;
            }
        }

        .dataTables_filter label {

            input {
                max-width: 150px;
            }
        }

        .dataTables_wrapper {
            padding: 0.5rem;
        }
    }
}