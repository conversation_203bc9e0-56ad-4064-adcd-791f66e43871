jQuery(function ($) {
    console.log("Datatable-helper.js is ready.");

    window.initializeDataTable = function ($table, searchInputSelector = '#custom-search') {
        if (!$.fn.DataTable || !$table.length) return;
    
        if ($.fn.DataTable.isDataTable($table)) {
            $table.DataTable().clear().destroy();
        }
    
        $table.DataTable({
            responsive: false,
            lengthChange: false,
            autoWidth: false,
            dom: '<"table-responsive"t><"c-info"i><"c-paginate"p>',
            language: {
                zeroRecords: "ไม่พบข้อมูล",
                info: "แสดงหน้า _PAGE_ จาก _PAGES_",
                infoEmpty: "ไม่มีข้อมูล",
                infoFiltered: "(ค้นหาจาก _MAX_ รายการ)",
                paginate: { next: "ถัดไป", previous: "ก่อนหน้า" }
            },
            initComplete: function () {
                let searchTimer;
                $(searchInputSelector).on('keyup', function () {
                    clearTimeout(searchTimer);
                    searchTimer = setTimeout(() => {
                        $table.DataTable().search(this.value).draw();
                    }, 200);
                });
            }
        });
    };    
});