/**
 * BAM DataTable Helper JavaScript Module
 * จัดการการสร้างและกำหนดค่า DataTable สำหรับระบบ BAM
 */
(function($) {
    'use strict';

    /**
     * คลาสสำหรับจัดการ DataTable
     */
    class BAMDataTableManager {
        constructor() {
            // ค่าคงที่สำหรับการจัดการ DataTable
            this.config = {
                defaultSearchSelector: '#custom-search',
                searchDelay: 200,
                defaultOptions: {
                    responsive: false,
                    lengthChange: false,
                    autoWidth: false,
                    dom: '<"table-responsive"t><"c-info"i><"c-paginate"p>',
                    pageLength: 10,
                    ordering: true,
                    searching: true,
                    paging: true,
                    info: true
                }
            };

            // ภาษาไทยสำหรับ DataTable
            this.thaiLanguage = {
                zeroRecords: "ไม่พบข้อมูล",
                info: "แสดงหน้า _PAGE_ จาก _PAGES_",
                infoEmpty: "ไม่มีข้อมูล",
                infoFiltered: "(ค้นหาจาก _MAX_ รายการ)",
                paginate: {
                    next: "ถัดไป",
                    previous: "ก่อนหน้า",
                    first: "หน้าแรก",
                    last: "หน้าสุดท้าย"
                },
                lengthMenu: "แสดง _MENU_ รายการต่อหน้า",
                search: "ค้นหา:",
                processing: "กำลังประมวลผล...",
                loadingRecords: "กำลังโหลดข้อมูล...",
                emptyTable: "ไม่มีข้อมูลในตาราง"
            };

            // เก็บ reference ของ search timers
            this.searchTimers = new Map();

            this.init();
        }

        /**
         * เริ่มต้นการทำงานของโมดูล
         */
        init() {
            console.log('BAM DataTable Manager initialized.');
        }

        /**
         * สร้าง DataTable พร้อมการตั้งค่าที่กำหนด
         * @param {jQuery} $table - jQuery object ของตาราง
         * @param {Object} options - ตัวเลือกเพิ่มเติม
         * @returns {Object|null} DataTable instance หรือ null
         */
        createDataTable($table, options = {}) {
            // ตรวจสอบความถูกต้องของ input
            if (!this.validateInputs($table)) {
                return null;
            }

            // ทำลาย DataTable เดิมถ้ามี
            this.destroyExistingDataTable($table);

            // รวมการตั้งค่า
            const finalOptions = this.mergeOptions(options);

            // สร้าง DataTable ใหม่
            return this.initializeDataTable($table, finalOptions);
        }

        /**
         * ตรวจสอบความถูกต้องของ inputs
         * @param {jQuery} $table - jQuery object ของตาราง
         * @returns {boolean}
         */
        validateInputs($table) {
            // ตรวจสอบว่า DataTable plugin มีอยู่หรือไม่
            if (!$.fn.DataTable) {
                console.error('DataTable plugin not found');
                return false;
            }

            // ตรวจสอบว่า table element มีอยู่หรือไม่
            if (!$table || !$table.length) {
                console.warn('Table element not found or empty');
                return false;
            }

            // ตรวจสอบว่าเป็น table element จริงหรือไม่
            if (!$table.is('table')) {
                console.error('Element is not a table');
                return false;
            }

            return true;
        }

        /**
         * ทำลาย DataTable ที่มีอยู่แล้ว
         * @param {jQuery} $table - jQuery object ของตาราง
         */
        destroyExistingDataTable($table) {
            if ($.fn.DataTable.isDataTable($table)) {
                try {
                    const tableId = $table.attr('id') || 'unknown';
                    console.log(`Destroying existing DataTable: ${tableId}`);

                    $table.DataTable().clear().destroy();

                    // ล้าง search timer ที่เกี่ยวข้อง
                    this.clearSearchTimer(tableId);
                } catch (error) {
                    console.error('Error destroying DataTable:', error);
                }
            }
        }

        /**
         * รวมการตั้งค่าเริ่มต้นกับตัวเลือกที่ส่งมา
         * @param {Object} customOptions - ตัวเลือกที่กำหนดเอง
         * @returns {Object} การตั้งค่าที่รวมแล้ว
         */
        mergeOptions(customOptions) {
            const baseOptions = {
                ...this.config.defaultOptions,
                language: this.thaiLanguage
            };

            // รวมการตั้งค่า โดยให้ customOptions มีความสำคัญสูงกว่า
            const mergedOptions = this.deepMerge(baseOptions, customOptions);

            // เพิ่ม initComplete callback
            mergedOptions.initComplete = (settings) => {
                this.handleInitComplete(settings, customOptions);
            };

            return mergedOptions;
        }

        /**
         * Deep merge objects
         * @param {Object} target - object เป้าหมาย
         * @param {Object} source - object ต้นทาง
         * @returns {Object} object ที่ merge แล้ว
         */
        deepMerge(target, source) {
            const result = { ...target };

            for (const key in source) {
                if (source.hasOwnProperty(key)) {
                    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                        result[key] = this.deepMerge(result[key] || {}, source[key]);
                    } else {
                        result[key] = source[key];
                    }
                }
            }

            return result;
        }

        /**
         * สร้าง DataTable instance
         * @param {jQuery} $table - jQuery object ของตาราง
         * @param {Object} options - การตั้งค่า DataTable
         * @returns {Object|null} DataTable instance
         */
        initializeDataTable($table, options) {
            try {
                const dataTable = $table.DataTable(options);

                const tableId = $table.attr('id') || 'table_' + Date.now();
                console.log(`DataTable initialized successfully: ${tableId}`);

                return dataTable;
            } catch (error) {
                console.error('Error initializing DataTable:', error);
                return null;
            }
        }

        /**
         * จัดการเมื่อ DataTable เริ่มต้นเสร็จ
         * @param {Object} settings - DataTable settings
         * @param {Object} customOptions - ตัวเลือกที่กำหนดเอง
         */
        handleInitComplete(settings, customOptions) {
            const $table = $(settings.nTable);
            const tableId = $table.attr('id') || 'unknown';

            // ตั้งค่า search functionality
            this.setupSearchFunctionality($table, customOptions);

            // เรียก custom initComplete callback ถ้ามี
            if (customOptions.initComplete && typeof customOptions.initComplete === 'function') {
                customOptions.initComplete.call(this, settings);
            }

            console.log(`DataTable initComplete: ${tableId}`);
        }

        /**
         * ตั้งค่าการค้นหาสำหรับ DataTable
         * @param {jQuery} $table - jQuery object ของตาราง
         * @param {Object} options - ตัวเลือกการตั้งค่า
         */
        setupSearchFunctionality($table, options) {
            const searchSelector = options.searchInputSelector || this.config.defaultSearchSelector;
            const $searchInput = $(searchSelector);

            if (!$searchInput.length) {
                console.warn(`Search input not found: ${searchSelector}`);
                return;
            }

            const tableId = $table.attr('id') || 'unknown';
            const searchDelay = options.searchDelay || this.config.searchDelay;

            // ล้าง event listener เดิม
            $searchInput.off('keyup.datatable search.datatable input.datatable');

            // เพิ่ม event listener ใหม่
            $searchInput.on('keyup.datatable search.datatable input.datatable', (e) => {
                this.handleSearchInput(e, $table, tableId, searchDelay);
            });
        }

        /**
         * จัดการ input ในช่องค้นหา
         * @param {Event} event - Event object
         * @param {jQuery} $table - jQuery object ของตาราง
         * @param {string} tableId - ID ของตาราง
         * @param {number} delay - ความล่าช้าในการค้นหา (ms)
         */
        handleSearchInput(event, $table, tableId, delay) {
            const searchValue = this.sanitizeSearchInput(event.target.value);

            // ล้าง timer เดิม
            this.clearSearchTimer(tableId);

            // ตั้ง timer ใหม่
            const timer = setTimeout(() => {
                this.performSearch($table, searchValue);
                this.searchTimers.delete(tableId);
            }, delay);

            this.searchTimers.set(tableId, timer);
        }

        /**
         * ทำความสะอาด search input
         * @param {string} input - ข้อความที่ต้องทำความสะอาด
         * @returns {string} ข้อความที่ทำความสะอาดแล้ว
         */
        sanitizeSearchInput(input) {
            if (typeof input !== 'string') {
                return '';
            }

            // ลบ HTML tags และ trim whitespace
            return input.replace(/<[^>]*>/g, '').trim();
        }

        /**
         * ทำการค้นหาใน DataTable
         * @param {jQuery} $table - jQuery object ของตาราง
         * @param {string} searchValue - ค่าที่ต้องการค้นหา
         */
        performSearch($table, searchValue) {
            try {
                const dataTable = $table.DataTable();
                dataTable.search(searchValue).draw();
            } catch (error) {
                console.error('Error performing search:', error);
            }
        }

        /**
         * ล้าง search timer
         * @param {string} tableId - ID ของตาราง
         */
        clearSearchTimer(tableId) {
            if (this.searchTimers.has(tableId)) {
                clearTimeout(this.searchTimers.get(tableId));
                this.searchTimers.delete(tableId);
            }
        }

        /**
         * ล้าง search timers ทั้งหมด
         */
        clearAllSearchTimers() {
            this.searchTimers.forEach((timer) => {
                clearTimeout(timer);
            });
            this.searchTimers.clear();
        }

        /**
         * ทำลาย DataTable และล้าง resources
         * @param {jQuery} $table - jQuery object ของตาราง
         */
        destroyDataTable($table) {
            if (!$table || !$table.length) {
                return;
            }

            const tableId = $table.attr('id') || 'unknown';

            // ล้าง search timer
            this.clearSearchTimer(tableId);

            // ทำลาย DataTable
            this.destroyExistingDataTable($table);
        }

        /**
         * รีเฟรช DataTable
         * @param {jQuery} $table - jQuery object ของตาราง
         * @param {Object} options - ตัวเลือกใหม่ (optional)
         * @returns {Object|null} DataTable instance ใหม่
         */
        refreshDataTable($table, options = {}) {
            this.destroyDataTable($table);
            return this.createDataTable($table, options);
        }

        /**
         * ดึงการตั้งค่าเริ่มต้นสำหรับ responsive table
         * @returns {Object} การตั้งค่า responsive
         */
        getResponsiveOptions() {
            return {
                responsive: {
                    details: {
                        type: 'column',
                        target: 'tr'
                    }
                },
                columnDefs: [{
                    className: 'control',
                    orderable: false,
                    targets: 0
                }]
            };
        }

        /**
         * ดึงการตั้งค่าเริ่มต้นสำหรับ table ที่มี buttons
         * @param {Array} buttons - รายการปุ่ม
         * @returns {Object} การตั้งค่า buttons
         */
        getButtonsOptions(buttons = ['copy', 'excel', 'pdf', 'print']) {
            return {
                dom: 'Bfrtip',
                buttons: buttons.map(btn => ({
                    extend: btn,
                    text: this.getButtonText(btn),
                    className: 'btn btn-secondary btn-sm'
                }))
            };
        }

        /**
         * ดึงข้อความปุ่มเป็นภาษาไทย
         * @param {string} buttonType - ประเภทปุ่ม
         * @returns {string} ข้อความปุ่ม
         */
        getButtonText(buttonType) {
            const buttonTexts = {
                copy: 'คัดลอก',
                excel: 'Excel',
                pdf: 'PDF',
                print: 'พิมพ์',
                csv: 'CSV'
            };

            return buttonTexts[buttonType] || buttonType;
        }
    }

    /**
     * เริ่มต้นโมดูลเมื่อ DOM พร้อม
     */
    $(function() {
        // สร้าง instance ของ BAMDataTableManager
        const dataTableManager = new BAMDataTableManager();

        // เก็บ reference ไว้ใน window object สำหรับการเข้าถึงจากภายนอก
        window.bamDataTableManager = dataTableManager;

        // รักษาความเข้ากันได้กับโค้ดเดิม
        window.initializeDataTable = function($table, searchInputSelector = '#custom-search') {
            return dataTableManager.createDataTable($table, {
                searchInputSelector: searchInputSelector
            });
        };

        // ฟังก์ชันเพิ่มเติมสำหรับการใช้งานขั้นสูง
        window.createResponsiveDataTable = function($table, options = {}) {
            const responsiveOptions = dataTableManager.getResponsiveOptions();
            const mergedOptions = { ...responsiveOptions, ...options };
            return dataTableManager.createDataTable($table, mergedOptions);
        };

        window.createDataTableWithButtons = function($table, buttons, options = {}) {
            const buttonOptions = dataTableManager.getButtonsOptions(buttons);
            const mergedOptions = { ...buttonOptions, ...options };
            return dataTableManager.createDataTable($table, mergedOptions);
        };

        // ล้าง resources เมื่อออกจากหน้า
        $(window).on('beforeunload', () => {
            if (window.bamDataTableManager) {
                window.bamDataTableManager.clearAllSearchTimers();
            }
        });
    });

})(jQuery);