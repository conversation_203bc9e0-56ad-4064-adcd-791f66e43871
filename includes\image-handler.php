<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

function handle_id_card_upload($input_name = 'nid_card_image') {
    if (empty($_FILES[$input_name]['name'])) {
        return null;
    }

    $file = $_FILES[$input_name];

    if (!is_valid_image_type($file['type']) || !is_real_image($file['tmp_name'])) {
        throw new Exception("ไฟล์รูปภาพไม่ปลอดภัยหรือไม่ถูกต้อง");
    }

    if ($file['size'] > 2 * 1024 * 1024) {
        throw new Exception("ขนาดรูปภาพต้องไม่เกิน 2MB");
    }

    $upload = wp_handle_upload($file, ['test_form' => false]);
    if (isset($upload['url'])) {
        return esc_url_raw($upload['url']);
    } else {
        throw new Exception("อัปโหลดรูปภาพล้มเหลว: " . $upload['error']);
    }
}
?>