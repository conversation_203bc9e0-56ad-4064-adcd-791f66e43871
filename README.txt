# ฉันต้องการสร้างระบบจัดการบัญชีโดยใช้ระบบการจัดการทั่วไป (CRUD) ซึ่งจะมีรายละเอียด ดังนี้

BAM System
Bank Account Management System

# Hosting ผู้ให้บริการ Namecheap
    - Domain : bam-sys.com
    - WordPress Hosting : EasyWP Turbo

# ฟังก์ชันที่ต้องมีในระบบจัดการบัญชีธนาคาร (CRUD. System)

1. ฟังก์ชันหลักตามแนวทาง CRUD
    C : Create (สร้าง)
        - เพิ่มบัญชีธนาคาร
    R : Read (อ่าน / เรียกดู)
        - แสดงบัญชีทั้งหมดตามประเภทธนาคาร (f789, s888, jnp, kaw, czo)
    U : Update (อัปเดท)
        - แก้ไขข้อมูลบัญชีธนาคาร
        - อัปเดตสถานะ เช่น ใช้งาน, ยกเลิก, เตรียมพร้อม
    D : Delete (ลบ)
        - ลบบัญชีธนาคาร

2. ฟังก์ชันระบบย่อย
    บัญชีธนาคาร
        - ฟอร์มเพิ่มบัญชี (add-bank.php)
        - ตารางแสดงบัญชี (bank.php)
        - จัดการ preview รูปบัตรประชาชน
    ภาพประจำตัวประชาชน
        - อัปโหลด/แสดง preview ภาพก่อน submit
        - เก็บ path รูปในฐานข้อมูล nid_card_image_path

3. ฟังก์ชันฝั่ง Frontend / UI
    AJAX Navigation
        - โหลดหน้าใหม่แบบไม่ refresh (navigatePage)
    Form Handler
        - Submit form เพิ่มบัญชีแบบ AJAX (form-handler.js)
    DataTable Helper
        - สร้างตารางบัญชีพร้อมค้นหา/แบ่งหน้า (datatable-helper.js)
    Menu UI
        - ปรับสถานะ active ของเมนู sidebar (core.js)
    ภาพ Upload
        - แสดง preview รูปบัตรประชาชน แบบ base64 และจาก path ใน database (form.css, form-handler.js)

4. โครงสร้างฐานข้อมูลที่รองรับ
    wp_bank_account
        - เก็บข้อมูลบัญชีธนาคารหลัก

# หมายเหตุ
    1. พัฒนาเว็บไซต์บน WordPress และใช้ฐานข้อมูลของ WordPress แต่ระบบที่พัฒนาเป็นระบบเฉพาะทางไม่เกี่ยวข้องกับการใช้งาน Wordpress โดยตรง
    2. การโหลดเนื้อหา Admin Dashboard Template จะโหลดแบบ ajax เพื่อป้องการไม่ให้หน้าเว็บรีโหลด เพื่อประสบการณ์การใช้งานที่ดีของผู้ใช้
    3. การพัฒนาระบบจะต้องคำนึงถึงความปลอดภัยเป็นอันดับแรกก่อนเสมอ โดยใช้มาตราฐาน OWASP Top 10

# Database Wordpress
1. PHP MyAdmin

# เครื่องมือที่ใช้งาน
1. wordpress
2. vs code

# Programming Languages
1. JavaScript (Jquery)
2. PHP

# Scripting / Query Languages
1. MySQL 5.7.35

# Markup & Style Languages
1. HTML
2. CSS

# library
1. Bootstrap v5.1.5
2. Jquery v3.7.1
3. Admin lte v4.0 (Bata 3)
4. Bootstrap icon v1.11.3

# ไฟล์ที่จำเป็นสำหรับ Wordpress
1. index.php (หน้าหลัก)
2. header.php (ส่วนหัวของเว็บไซต์)
3. page.php (หน้า)
4. single.php (เรื่อง)
5. footer.php (ส่วนท้ายของเว็บไซต์)
6. functions.php (สำหรับลงทะเบียนฟังชั่นต่างๆ)
7. style.css (StyleSheet)

# ไฟล์ที่จำเป็นสำหรับ Admin Dashboard Template
1. sidebar.php (Sidebar Menu)
2. main-content.php (Main Content โหลดเนื้อหาของหน้าใน ajax)
3. navbar.php (Navbar Menu)
4. add-bank.php (Form Add Bank Account)
5. bank.php (Table Bank Account)
6. add-oag-code.php (Form Add OAG Code)
7. oag-code.php (Table OAG Code)

# ขั้นตอนการเริ่มทำงาน
1. เริ่มสร้างฐานข้อมูลบัญชีที่ใช้งานอยู่ ณ ปัจจุบัน
    1.1 wp_bank_account

2. เริ่มสร้างฐานข้อมูลประวัติการใช้งานบัญชี
    2.1 wp_bank_account_history

3. สร้างไฟล์ที่จำเป็นสำหรับ Wordpress ได้แก่
    3.1 index.php (หน้าหลัก)
    3.2 header.php (ส่วนหัวของเว็บไซต์)
    3.3 page.php (หน้า)
    3.4 single.php (เรื่อง)
    3.5 footer.php (ส่วนท้ายของเว็บไซต์)
    3.6 functions.php (สำหรับลงทะเบียนฟังชั่นต่างๆ)
    3.7 style.css (StyleSheet)

4. ติดตั้งธีม Custom Theme

5. เรียกใช้ library ต่างๆ ได้แก่
    5.1 Jquery
    5.2 Bootstrap
    5.3 Admin Lte
    5.4 Font Awesome / Bootstrap Icon

6. สร้าง Admin Dashboard Template
    6.1 sidebar.php (Sidebar Menu)
    6.2 main-content.php (Main Content โหลดเนื้อหาของหน้าใน ajax)
    6.3 navbar.php (Navbar Menu)
    6.4 add-bank.php (Form Add Bank Account)
    6.5 bank.php (Table Bank Account)
    6.6 add-oag-code.php (Form Add OAG Code)
    6.7 oag-code.php (Table OAG Code)

CREATE TABLE wp_bank_account (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,                                -- ID (PK)
    bank_type ENUM('f789', 's888', 'jnp', 'kaw', 'czo') NOT NULL,              -- Bank Type
    bank_name ENUM('ธนาคารไทยพาณิชย์','ธนาคารกสิกรไทย','ธนาคารกรุงไทย') NOT NULL, -- Bank Name
    account_name VARCHAR(100) NOT NULL,                                        -- Account Name
    bank_number VARCHAR(20) NOT NULL UNIQUE,                                   -- Bank Number
    atm_pin CHAR(6),                                                           -- ATM Pin
    app_pin CHAR(6),                                                           -- Application Pin
    agent_name VARCHAR(10),                                                    -- Agent Name
    phone_number CHAR(10),                                                     -- Phone Number
    birth_date DATE,                                                           -- Birth Date
    national_id VARCHAR(20),                                                   -- National ID
    nid_card_image_path VARCHAR(255),                                          -- ID Card Image Path
    notes TEXT,                                                                -- Notes
    activated_date DATE,                                                       -- Activated Date
    canceled_date DATE,                                                        -- Canceled Date
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,                          -- Created Date
    status ENUM('ใช้งาน', 'ยกเลิก', 'เตรียมพร้อม') NOT NULL                        -- Status (active/cancel/standby)
);

CREATE TABLE wp_userrole (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,                                -- ID (PK)
    role_name VARCHAR(100) NOT NULL,                                           -- Role Name

    -- สิทธิ์แบบละเอียด --
    can_access_dashboard TINYINT(1) DEFAULT 0,                                 -- can access dashboard page
    can_access_assistant TINYINT(1) DEFAULT 0,                                 -- can access assistant page
    can_access_userrole TINYINT(1) DEFAULT 0,                                  -- can access userrole page
    can_access_f789 TINYINT(1) DEFAULT 0,                                      -- can access f789 page
    can_access_s888 TINYINT(1) DEFAULT 0,                                      -- can access s888 page
    can_access_jnp TINYINT(1) DEFAULT 0,                                       -- can access jnp page
    can_access_kaw TINYINT(1) DEFAULT 0,                                       -- can access kaw page
    can_access_czo TINYINT(1) DEFAULT 0,                                       -- can access czo page
    can_access_add_bank_f789 TINYINT(1) DEFAULT 0,                             -- can access add bank f789 page
    can_access_add_bank_s888 TINYINT(1) DEFAULT 0,                             -- can access add bank s888 page
    can_access_add_bank_jnp TINYINT(1) DEFAULT 0,                              -- can access add bank jnp page
    can_access_add_bank_kaw TINYINT(1) DEFAULT 0,                              -- can access add bank kaw page
    can_access_add_bank_czo TINYINT(1) DEFAULT 0,                              -- can access add bank czo page
    can_access_add_assistance TINYINT(1) DEFAULT 0,                            -- can access add assistance page

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP                             -- Created Date
);

CREATE TABLE wp_assistance (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,                                -- ID (PK)
    username VARCHAR(100) NOT NULL UNIQUE,                                     -- username
    password_hash VARCHAR(255) NOT NULL,                                       -- password_hash
    role_id INT UNSIGNED NOT NULL,                                             -- FK to wp_userrole.id
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,                            -- Created Date
    last_login DATETIME DEFAULT NULL,                                          -- Last Login Date
    is_active TINYINT(1) DEFAULT 1,                                            -- Is Active

    CONSTRAINT fk_assistance_role
        FOREIGN KEY (role_id)
        REFERENCES wp_userrole(id)
        ON DELETE CASCADE
);

# โครงสร้างไฟล์/โฟลเดอร์
/bam-sys-theme/
│
├── assets/
│   ├── css/
│   │   ├── form.css                        -- ฟอร์มบัญชี, upload preview
│   │   ├── utilities-v2.css                -- คลาสช่วย เช่น .text-xxx, .bg-xxx
│   │   ├── my-style.css                    -- Style รวม (หรือ custom theme)
│   │   ├── page-login.css                  -- Style ของหน้า Login
│   │   ├── data-tables/
│   │   │   └── data-table.css              -- ตาราง DataTable UI
│   │   └── plugins/                        -- ที่เก็บ Style ภายนอก เช่น sweetalert, adminlte
│   │
│   └── js/
│       ├── core.js                         -- AJAX Navigation, Sidebar Menu
│       ├── sweetalert-handler.js           -- การจัดการ Sweetalert
│       ├── form-handler.js                 -- การจัดการ submit form (Add/Edit)
│       ├── data-tables/
│       │   └── datatable-helper.js         -- กำหนด DataTable UI
│       ├── pages/
│       │   ├── assistance.js               -- โหลดตารางผู้ช่วย
│       │   ├── bank.js                     -- โหลดตารางบัญชี
│       │   ├── userrole.js                 -- โหลดตารางสิทธิ์ผู้ช่วย
│       │   └── login.js                    -- ตัวจัดการ Ajax login
│       └── plugins/                        -- ที่เก็บ script ภายนอก เช่น sweetalert, adminlte
│
├── templates/
│   └── pages/
│       ├── add-bank.php                    -- แบบฟอร์มเพิ่มบัญชี
│       ├── add-assistance.php              -- แบบฟอร์มเพิ่มผู้ช่วย
│       ├── assistance.php                  -- ตารางผู้ช่วย
│       ├── userrole.php                    -- ตารางสิทธิ์ผู้ช่วย
│       ├── bank.php                        -- ตารางบัญชีธนาคาร
│       └── login.php                       -- หน้า login
│
├── includes/
│   ├── bank-validator.php                  -- ตรวจสอบข้อมูล
│   ├── encryption.php                      -- เข้า/ถอด ข้อมูล
│   └── image-handler.php                   -- จัดการรูป
│
├── layout/
│   ├── herder.php                          -- Header ของระบบ
│   ├── footer.php                          -- Footer ของระบบ
│   ├── sidebar.php                         -- Sidebar Menu
│   ├── navbar.php                          -- Navbar Menu
│   └── main-content.php                    -- Main Content โหลดเนื้อหาของหน้าใน ajax
│
├── index.php                               -- หน้าหลักของระบบ
├── functions.php                           -- ลงทะเบียน style/script และ shortcode
├── page-login.php                          -- หน้า login
└── style.css                               -- เรียกใช้ style หลัก WordPress