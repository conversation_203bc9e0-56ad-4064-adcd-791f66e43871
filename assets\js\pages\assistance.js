jQuery(function ($) {
    console.log("Display-assistance.js is ready.");

    window.initializeAssistanceTables = function () {
        const $table = $('#display_assistance');
        if (!$table.length) return;

        $.ajax({
            url: bamAjaxConfig.ajaxurl,
            method: 'GET',
            data: {
                action: 'load_assistance_data',
                nonce: bamAjaxConfig.nonce
            },
            success: function (response) {
                if (response.success && Array.isArray(response.data)) {
                    const rows = response.data.map((item, index) => `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${item.username}</td>
                            <td>${item.created_at}</td>
                            <td>${item.last_login}</td>
                            <td class="text-center">
                                <div class="dropdown">
                                    <button class="btn btn-primary" type="button" data-bs-toggle="dropdown">ดำเนินการ <i class="bi bi-chevron-down"></i></button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item text-white" href="#" data-action="view" data-id="${item.id}"><i class="bi bi-info-circle-fill"></i> ดูข้อมูล</a></li>
                                        <li><a class="dropdown-item text-white" href="#" data-action="edit" data-id="${item.id}"><i class="bi bi-gear-fill"></i> แก้ไข</a></li>
                                        <li><a class="dropdown-item text-danger" href="#" data-action="delete" data-id="${item.id}"><i class="bi bi-trash-fill"></i> ลบ</a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    `).join('');

                    $table.find('tbody').html(rows);

                    if (typeof initializeDataTable === 'function') {
                        initializeDataTable($table);
                    }
                } else {
                    console.warn("โหลดข้อมูลผู้ช่วยไม่สำเร็จ");
                }
            },
            error: function () {
                console.error("เกิดข้อผิดพลาดในการโหลดข้อมูลผู้ช่วยจาก AJAX");
            }
        });
    };

    if ($('#display_assistance').length > 0 && typeof window.initializeAssistanceTables === 'function') {
        window.initializeAssistanceTables();
    }
});