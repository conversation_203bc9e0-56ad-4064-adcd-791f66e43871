jQuery(function ($) {
    console.log("Sweetalert-handler.js is ready.");

    function showSuccessAlert(message, currentBankType) {
        Swal.fire({
            icon: 'success',
            title: 'สำเร็จ!',
            text: message,
            confirmButtonText: 'ตกลง',
            timer: 2000,
            timerProgressBar: true,
            // showConfirmButton: true,
            didOpen: () => {
                if (typeof navigatePage === 'function') {
                    navigatePage(null, '/' + currentBankType + '?refresh=' + Date.now());
                }
            }
        });
    }

    function showErrorAlert(message) {
        Swal.fire({
            icon: 'error',
            title: 'ผิดพลาด!',
            text: message,
            confirmButtonText: 'ตกลง',
            // showConfirmButton: true,
        });
    }
    
    // global functions เรียกใช้ได้จากทุกไฟล์
    window.showSuccessAlert = showSuccessAlert;
    window.showErrorAlert = showErrorAlert;
});