/**
 * BAM SweetAlert Handler JavaScript Module
 * จัดการการแสดงผล SweetAlert notifications ในระบบ BAM
 */
(function($) {
    'use strict';

    /**
     * คลาสสำหรับจัดการ SweetAlert
     */
    class BAMSweetAlertManager {
        constructor() {
            // ค่าคงที่สำหรับการจัดการ SweetAlert
            this.config = {
                defaultTimer: 2000,
                defaultTimerProgressBar: true,
                defaultShowConfirmButton: true,
                defaultAllowOutsideClick: true,
                defaultAllowEscapeKey: true
            };

            // ข้อความเริ่มต้นสำหรับแต่ละประเภท
            this.defaultTitles = {
                success: 'สำเร็จ!',
                error: 'ผิดพลาด!',
                warning: 'คำเตือน!',
                info: 'ข้อมูล',
                question: 'ยืนยัน'
            };

            // ข้อความปุ่มเริ่มต้น
            this.defaultButtonTexts = {
                confirm: 'ตกลง',
                cancel: 'ยกเลิก',
                yes: 'ใช่',
                no: 'ไม่ใช่',
                delete: 'ลบ',
                save: 'บันทึก'
            };

            // สีของปุ่มตามประเภท
            this.buttonColors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8',
                question: '#6c757d',
                delete: '#dc3545',
                primary: '#007bff'
            };

            this.init();
        }

        /**
         * เริ่มต้นการทำงานของโมดูล
         */
        init() {
            console.log('BAM SweetAlert Manager initialized.');
        }

        /**
         * แสดง alert พื้นฐาน
         * @param {Object} options - ตัวเลือกสำหรับ SweetAlert
         * @returns {Promise} SweetAlert promise
         */
        showAlert(options = {}) {
            // ตรวจสอบว่า Swal มีอยู่หรือไม่
            if (typeof Swal === 'undefined') {
                console.error('SweetAlert2 library not found');
                return this.fallbackAlert(options.text || options.title || 'Alert');
            }

            // รวมการตั้งค่าเริ่มต้น
            const finalOptions = this.mergeOptions(options);

            // ทำความสะอาดข้อมูล
            const sanitizedOptions = this.sanitizeOptions(finalOptions);

            return Swal.fire(sanitizedOptions);
        }

        /**
         * รวมการตั้งค่าเริ่มต้นกับตัวเลือกที่ส่งมา
         * @param {Object} options - ตัวเลือกที่ส่งมา
         * @returns {Object} การตั้งค่าที่รวมแล้ว
         */
        mergeOptions(options) {
            const baseOptions = {
                confirmButtonText: this.defaultButtonTexts.confirm,
                allowOutsideClick: this.config.defaultAllowOutsideClick,
                allowEscapeKey: this.config.defaultAllowEscapeKey,
                showConfirmButton: this.config.defaultShowConfirmButton
            };

            // เพิ่ม title เริ่มต้นตาม icon
            if (options.icon && this.defaultTitles[options.icon] && !options.title) {
                baseOptions.title = this.defaultTitles[options.icon];
            }

            // เพิ่มสีปุ่มตาม icon
            if (options.icon && this.buttonColors[options.icon]) {
                baseOptions.confirmButtonColor = this.buttonColors[options.icon];
            }

            return { ...baseOptions, ...options };
        }

        /**
         * ทำความสะอาดตัวเลือก SweetAlert
         * @param {Object} options - ตัวเลือกที่ต้องทำความสะอาด
         * @returns {Object} ตัวเลือกที่ทำความสะอาดแล้ว
         */
        sanitizeOptions(options) {
            const sanitized = { ...options };

            // ทำความสะอาดข้อความ
            if (sanitized.title) {
                sanitized.title = this.sanitizeText(sanitized.title);
            }
            if (sanitized.text) {
                sanitized.text = this.sanitizeText(sanitized.text);
            }
            if (sanitized.html) {
                sanitized.html = this.sanitizeHtml(sanitized.html);
            }

            // ตรวจสอบค่าตัวเลข
            if (sanitized.timer && typeof sanitized.timer !== 'number') {
                sanitized.timer = parseInt(sanitized.timer) || this.config.defaultTimer;
            }

            return sanitized;
        }

        /**
         * ทำความสะอาดข้อความ
         * @param {string} text - ข้อความที่ต้องทำความสะอาด
         * @returns {string} ข้อความที่ทำความสะอาดแล้ว
         */
        sanitizeText(text) {
            if (typeof text !== 'string') {
                return String(text || '');
            }

            // ลบ HTML tags และ trim whitespace
            return text.replace(/<[^>]*>/g, '').trim();
        }

        /**
         * ทำความสะอาด HTML (อนุญาตเฉพาะ tags ที่ปลอดภัย)
         * @param {string} html - HTML ที่ต้องทำความสะอาด
         * @returns {string} HTML ที่ทำความสะอาดแล้ว
         */
        sanitizeHtml(html) {
            if (typeof html !== 'string') {
                return String(html || '');
            }

            // อนุญาตเฉพาะ tags ที่ปลอดภัย
            const allowedTags = ['b', 'i', 'u', 'strong', 'em', 'br', 'p', 'span'];
            const tagRegex = /<\/?([a-zA-Z][a-zA-Z0-9]*)\b[^<>]*>/gi;

            return html.replace(tagRegex, (match, tagName) => {
                return allowedTags.includes(tagName.toLowerCase()) ? match : '';
            });
        }

        /**
         * Fallback alert เมื่อ SweetAlert ไม่พร้อมใช้งาน
         * @param {string} message - ข้อความ
         * @returns {Promise} Promise ที่ resolve ทันที
         */
        fallbackAlert(message) {
            alert(this.sanitizeText(message));
            return Promise.resolve({ isConfirmed: true });
        }

        /**
         * แสดง success alert
         * @param {string} message - ข้อความ
         * @param {string|Object} navigationTarget - เป้าหมายการนำทาง หรือ options object
         * @param {Object} additionalOptions - ตัวเลือกเพิ่มเติม
         * @returns {Promise} SweetAlert promise
         */
        showSuccess(message, navigationTarget = null, additionalOptions = {}) {
            const options = {
                icon: 'success',
                text: message,
                timer: this.config.defaultTimer,
                timerProgressBar: this.config.defaultTimerProgressBar,
                ...additionalOptions
            };

            // จัดการ navigation
            if (navigationTarget) {
                options.didOpen = () => {
                    this.handleNavigation(navigationTarget);
                };
            }

            return this.showAlert(options);
        }

        /**
         * แสดง error alert
         * @param {string} message - ข้อความ
         * @param {Object} additionalOptions - ตัวเลือกเพิ่มเติม
         * @returns {Promise} SweetAlert promise
         */
        showError(message, additionalOptions = {}) {
            const options = {
                icon: 'error',
                text: message,
                ...additionalOptions
            };

            return this.showAlert(options);
        }

        /**
         * แสดง warning alert
         * @param {string} message - ข้อความ
         * @param {Object} additionalOptions - ตัวเลือกเพิ่มเติม
         * @returns {Promise} SweetAlert promise
         */
        showWarning(message, additionalOptions = {}) {
            const options = {
                icon: 'warning',
                text: message,
                ...additionalOptions
            };

            return this.showAlert(options);
        }

        /**
         * แสดง info alert
         * @param {string} message - ข้อความ
         * @param {Object} additionalOptions - ตัวเลือกเพิ่มเติม
         * @returns {Promise} SweetAlert promise
         */
        showInfo(message, additionalOptions = {}) {
            const options = {
                icon: 'info',
                text: message,
                ...additionalOptions
            };

            return this.showAlert(options);
        }

        /**
         * แสดง confirmation dialog
         * @param {string} message - ข้อความ
         * @param {Object} additionalOptions - ตัวเลือกเพิ่มเติม
         * @returns {Promise} SweetAlert promise
         */
        showConfirmation(message, additionalOptions = {}) {
            const options = {
                icon: 'question',
                text: message,
                showCancelButton: true,
                confirmButtonText: this.defaultButtonTexts.yes,
                cancelButtonText: this.defaultButtonTexts.no,
                confirmButtonColor: this.buttonColors.primary,
                cancelButtonColor: this.buttonColors.error,
                ...additionalOptions
            };

            return this.showAlert(options);
        }

        /**
         * แสดง delete confirmation dialog
         * @param {string} message - ข้อความ (optional)
         * @param {Object} additionalOptions - ตัวเลือกเพิ่มเติม
         * @returns {Promise} SweetAlert promise
         */
        showDeleteConfirmation(message = 'คุณแน่ใจหรือไม่ที่จะลบข้อมูลนี้?', additionalOptions = {}) {
            const options = {
                icon: 'warning',
                text: message,
                showCancelButton: true,
                confirmButtonText: this.defaultButtonTexts.delete,
                cancelButtonText: this.defaultButtonTexts.cancel,
                confirmButtonColor: this.buttonColors.delete,
                cancelButtonColor: this.buttonColors.info,
                ...additionalOptions
            };

            return this.showAlert(options);
        }

        /**
         * แสดง loading alert
         * @param {string} message - ข้อความ
         * @param {Object} additionalOptions - ตัวเลือกเพิ่มเติม
         * @returns {Promise} SweetAlert promise
         */
        showLoading(message = 'กำลังประมวลผล...', additionalOptions = {}) {
            const options = {
                title: message,
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                },
                ...additionalOptions
            };

            return this.showAlert(options);
        }

        /**
         * ปิด SweetAlert ที่เปิดอยู่
         */
        close() {
            if (typeof Swal !== 'undefined' && Swal.isVisible()) {
                Swal.close();
            }
        }

        /**
         * จัดการการนำทาง
         * @param {string|Object} target - เป้าหมายการนำทาง
         */
        handleNavigation(target) {
            if (typeof target === 'string') {
                // ถ้าเป็น string ให้ถือว่าเป็น bank type
                this.navigateToPage(target);
            } else if (typeof target === 'object' && target.url) {
                // ถ้าเป็น object ที่มี url
                this.navigateToUrl(target.url, target.refresh);
            }
        }

        /**
         * นำทางไปยังหน้าธนาคาร
         * @param {string} bankType - ประเภทธนาคาร
         */
        navigateToPage(bankType) {
            const url = `/${bankType}?refresh=${Date.now()}`;
            this.navigateToUrl(url);
        }

        /**
         * นำทางไปยัง URL
         * @param {string} url - URL ปลายทาง
         * @param {boolean} refresh - เพิ่ม refresh parameter หรือไม่
         */
        navigateToUrl(url, refresh = true) {
            if (typeof navigatePage === 'function') {
                const finalUrl = refresh && !url.includes('refresh=')
                    ? `${url}${url.includes('?') ? '&' : '?'}refresh=${Date.now()}`
                    : url;

                navigatePage(null, finalUrl);
            } else {
                console.warn('navigatePage function not found, using window.location');
                window.location.href = url;
            }
        }

        /**
         * สร้าง toast notification
         * @param {string} message - ข้อความ
         * @param {string} icon - ประเภท icon
         * @param {Object} additionalOptions - ตัวเลือกเพิ่มเติม
         * @returns {Promise} SweetAlert promise
         */
        showToast(message, icon = 'success', additionalOptions = {}) {
            const options = {
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                icon: icon,
                title: message,
                didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer);
                    toast.addEventListener('mouseleave', Swal.resumeTimer);
                },
                ...additionalOptions
            };

            return this.showAlert(options);
        }
    }

    /**
     * เริ่มต้นโมดูลเมื่อ DOM พร้อม
     */
    $(function() {
        // สร้าง instance ของ BAMSweetAlertManager
        const sweetAlertManager = new BAMSweetAlertManager();

        // เก็บ reference ไว้ใน window object สำหรับการเข้าถึงจากภายนอก
        window.bamSweetAlertManager = sweetAlertManager;

        // รักษาความเข้ากันได้กับโค้ดเดิม
        window.showSuccessAlert = function(message, currentBankType) {
            return sweetAlertManager.showSuccess(message, currentBankType);
        };

        window.showErrorAlert = function(message) {
            return sweetAlertManager.showError(message);
        };

        // ฟังก์ชันเพิ่มเติมสำหรับการใช้งานขั้นสูง
        window.showWarningAlert = function(message, options = {}) {
            return sweetAlertManager.showWarning(message, options);
        };

        window.showInfoAlert = function(message, options = {}) {
            return sweetAlertManager.showInfo(message, options);
        };

        window.showConfirmationAlert = function(message, options = {}) {
            return sweetAlertManager.showConfirmation(message, options);
        };

        window.showDeleteConfirmationAlert = function(message, options = {}) {
            return sweetAlertManager.showDeleteConfirmation(message, options);
        };

        window.showLoadingAlert = function(message, options = {}) {
            return sweetAlertManager.showLoading(message, options);
        };

        window.showToastAlert = function(message, icon = 'success', options = {}) {
            return sweetAlertManager.showToast(message, icon, options);
        };

        window.closeSweetAlert = function() {
            sweetAlertManager.close();
        };
    });

})(jQuery);