<aside class="app-sidebar bg-body-secondary shadow overflow-hidden" data-bs-theme="dark">
    <div class="c-sidebar-brand d-flex align-items-center justify-content-start py-3 px-2 border-bottom fs-5 text-nowrap">
        <a href="../index.html" class="brand-link d-flex align-items-center text-decoration-none">
            <img src="https://yt3.ggpht.com/yti/ANjgQV8vZutCTvT9vmnGFYpOcP7-p_jw7lGaLC-koLXb3UiNnA=s88-c-k-c0x00ffffff-no-rj" alt="AdminLTE Logo" class="c-brand-image opacity-75 rounded-circle shadow float-start">
            <span class="c-brand-text fw-midium text-gray-400 ms-2 d-md-inline">BAM SYSTEM</span>
        </a>
    </div>
    <div class="c-sidebar-wrapper">
        <nav class="mt-3">
            <ul class="nav sidebar-menu flex-column" data-my-toggle="treeview" role="menu" data-accordion="false">
                <?php if (!empty($_SESSION['bam_permission']['can_access_dashboard'])): ?>
                <li class="nav-item">
                    <a href="/dashboard" id="dashboard" class="nav-link">
                        <i class="nav-icon bi bi-house-fill"></i>
                        <p>หน้าหลัก</p>
                    </a>
                </li>
                <?php endif; ?>
                <?php
                $hasBankPermission = false;
                $bankTypes = ['f789', 's888', 'jnp', 'kaw', 'czo'];

                foreach ($bankTypes as $type) {
                    if (!empty($_SESSION['bam_permission']["can_access_$type"])) {
                        $hasBankPermission = true;
                        break;
                    }
                }
                ?>
                <?php if ($hasBankPermission): ?>
                <li class="nav-item">
                    <a href="#" id="toggle-menu" class="nav-link">
                        <i class="nav-icon bi bi-wallet-fill"></i>
                        <p>จัดการบัญชี
                            <span class="nav-badge badge me-3">1</span>
                            <i class="nav-arrow bi bi-chevron-right"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <?php if (!empty($_SESSION['bam_permission']['can_access_f789'])): ?>
                        <li class="nav-item">
                            <a href="/f789" id="f789" class="nav-link">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Fufu789</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (!empty($_SESSION['bam_permission']['can_access_s888'])): ?>
                        <li class="nav-item">
                            <a href="/s888" id="s888" class="nav-link">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Swing888</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (!empty($_SESSION['bam_permission']['can_access_jnp'])): ?>
                        <li class="nav-item">
                            <a href="/jnp" id="jnp" class="nav-link">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Jaynaplus</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (!empty($_SESSION['bam_permission']['can_access_kaw'])): ?>
                        <li class="nav-item">
                            <a href="/kaw" id="kaw" class="nav-link">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Koawin</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (!empty($_SESSION['bam_permission']['can_access_czo'])): ?>
                        <li class="nav-item">
                            <a href="/czo" id="czo" class="nav-link">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Cezaone</p>
                                <span class="nav-badge badge me-3">New</span>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>
                <?php if (!empty($_SESSION['bam_permission']['can_access_assistance'])): ?>
                <li class="nav-item">
                    <a href="/assistance" id="assistance" class="nav-link">
                        <i class="bi bi-person-circle"></i>
                        <p>ผู้ช่วย</p>
                    </a>
                </li>
                <?php endif; ?>
                <?php if (!empty($_SESSION['bam_permission']['can_access_userrole'])): ?>
                <li class="nav-item">
                    <a href="/userrole" id="userrole" class="nav-link">
                        <i class="bi bi-person-fill-gear"></i>
                        <p>จัดการหน้าที่</p>
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a href="<?php echo wp_nonce_url(admin_url('admin-post.php?action=bam_logout'), 'bam_logout_nonce'); ?>" class="nav-link btn-logout bg-danger">
                        <i class="bi bi-box-arrow-left"></i>
                        <p>ออกจากระบบ</p>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
</aside>