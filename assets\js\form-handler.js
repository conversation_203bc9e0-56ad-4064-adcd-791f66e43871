jQuery(function ($) {
    console.log("Form-handler.js is ready.");

    // ตรวจสอบว่า bamAjaxConfig ถูกส่งมาจาก PHP
    if (typeof bamAjaxConfig !== 'undefined') {

        // ฟังก์ชันเริ่มต้นเมื่อโหลดฟอร์มบัญชีธนาคาร
        window.initializeBankForm = function () {
            $(document).off('submit', '#add-bank-form');

            const dynamicBankType = $('#add-bank-form input[name="bank_type"]').val();
            if (dynamicBankType) {
                bamAjaxConfig.bankType = dynamicBankType;
            }

            // แสดง preview จาก path ฐานข้อมูล (ฝั่ง PHP ใส่มาแล้ว)
            const imageUrl = $('#nid-preview').attr('src');
            if (imageUrl && imageUrl.trim() !== '' && imageUrl.trim().toLowerCase() !== 'null') {
                $('#nid-preview').show();
                $('#nid-preview-wrapper').addClass('has-image');
                $('#nid-icon, #nid-text').hide();
            } else {
                $('#nid-preview').hide();
                $('#nid-preview-wrapper').removeClass('has-image');
                $('#nid-icon, #nid-text').show();
            }

            // เมื่อผู้ใช้กด submit ฟอร์ม
            $(document).on('submit', '#add-bank-form', function (e) {
                e.preventDefault();

                const currentBankType = $('#add-bank-form input[name="bank_type"]').val();
                const formData = new FormData(this);
                formData.append('action', 'add_bank_ajax');
                formData.append('nonce', bamAjaxConfig.nonce);
                formData.append('bank_type', currentBankType);

                submitBankForm(formData, currentBankType);
            });

            // เมื่อเลือกไฟล์ใหม่ → แสดง preview แบบ base64
            $(document).on('change', '#nid-card-image', function () {
                const file = this.files[0];
                const reader = new FileReader();

                if (file && file.type.startsWith('image/')) {
                    reader.onload = function (e) {
                        $('#nid-preview')
                            .attr('src', e.target.result)
                            .show();

                        $('#nid-preview-wrapper').addClass('has-image');

                        // ซ่อน icon + text ด้านล่าง
                        $('#nid-icon, #nid-text').hide();
                    };
                    reader.readAsDataURL(file);
                } else {
                    $('#nid-preview').hide().attr('src', '');
                    $('#nid-preview-wrapper').removeClass('has-image');
                    $('#nid-icon, #nid-text').show(); // ❌ ไม่มีรูป → แสดง icon+text กลับมา
                }
            });
        };

        // ฟังก์ชันส่งฟอร์มผ่าน AJAX:Form add bank
        function submitBankForm(formData, currentBankType) {

            formData.append('nonce', bamAjaxConfig.nonce);
            
            $.ajax({
                url: bamAjaxConfig.ajaxurl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {

                    if (response.success) {
                        if (typeof showSuccessAlert === 'function') {
                            showSuccessAlert(response.data.message, currentBankType);
                        }
                    } else {
                        if (typeof showErrorAlert === 'function') {
                            showErrorAlert(response.data.message);
                        }
                    }
                },
                error: function (xhr, status, error) {

                    if (typeof showErrorAlert === 'function') {
                        showErrorAlert('ไม่สามารถส่งข้อมูลได้: ' + error);
                    }
                }
            });
        }
        
        // เรียก Js เมื่อกด F5
        if ($('#add-bank-form').length > 0) {
            initializeBankForm();
        }

        // ฟังก์ชันส่งฟอร์มผ่าน AJAX:Form add assistance
        $(document).on('submit', '#add-assistance-form', function (e) {
            e.preventDefault();

            const formData = {
                action: 'add_assistance_ajax',
                username: $('#username').val(),
                password: $('#password').val(),
                role_id: $('#role_id').val(),
                nonce: bamAjaxConfig.nonce,
            };

            $.post(bamAjaxConfig.ajaxurl, formData, function (response) {
                if (response.success) {
                    if (typeof showSuccessAlert === 'function') {
                        showSuccessAlert(response.data.message, 'assistance');
                    }
                } else {
                    if (typeof showErrorAlert === 'function') {
                        showErrorAlert(response.data.message);
                    }
                }
            }).fail(function () {
                if (typeof showErrorAlert === 'function') {
                    showErrorAlert('เกิดข้อผิดพลาดในการส่งข้อมูล');
                }
            });
        });
    }
});