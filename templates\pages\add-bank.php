<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

function add_bank_form($bank_type)
{
    ob_start();
?>
    <form id="add-bank-form" method="post" enctype="multipart/form-data">
        <input type="hidden" name="bank_type" value="<?php echo esc_attr($bank_type); ?>">
        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4 p-3">
                    <label for="bank-name">ธนาคาร</label>
                    <select class="form-select" id="bank-name" name="bank_name" required>
                        <option value="">กรุณาเลือกธนาคาร</option>
                        <option value="ธนาคารไทยพาณิชย์">ธนาคารไทยพาณิชย์</option>
                        <option value="ธนาคารกสิกรไทย">ธนาคารกสิกรไทย</option>
                        <option value="ธนาคารกรุงไทย">ธนาคารกรุงไทย</option>
                    </select><br>

                    <label for="account-name">ชื่อบัญชี</label>
                    <input type="text" class="form-control" id="account-name" name="account_name" placeholder="ชื่อจริง - นามสกุล" required><br>

                    <label for="bank-number">เลขบัญชีธนาคาร</label>
                    <input type="number" class="form-control" id="account-number" name="bank_number" placeholder="เลขบัญชีธนาคาร" required><br>

                    <label for="atm-pin">รหัสบัตร ATM</label>
                    <input type="number" class="form-control" id="atm-pin" name="atm_pin" placeholder="เลข 6 หลัก"><br>

                    <label for="app-pin">รหัสแอปพลิเคชัน</label>
                    <input type="number" class="form-control" id="app-pin" name="app_pin" placeholder="เลข 6 หลัก">
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-4 p-3">
                    <label for="agent-name">รหัสตัวแทน</label>
                    <input type="text" class="form-control" id="agent-name" name="agent_name" placeholder="รหัสตัวแทน"><br>

                    <label for="phone-number">เบอร์โทรศัพท์</label>
                    <input type="number" class="form-control" id="phone-number" name="phone_number" placeholder="เลข 10 หลัก"><br>

                    <label for="birth-date">วัน/เดือน/ปี เกิด</label>
                    <input type="date" class="form-control" id="birth-date" name="birth_date" placeholder="วัน/เดือน/ปี เกิด"><br>

                    <label for="national-id">เลขประจำตัวประชาชน</label>
                    <input type="number" class="form-control" id="national-id" name="national_id" placeholder="เลข 13 หลัก">
                </div>
            </div>
            <div class="col-md-4">
                <div class="card mb-4 p-3">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="upload-box justify-content-center align-items-center p-1 rounded-3">
                                <label for="nid-card-image" class="upload-label w-100 h-100 rounded-1 d-flex justify-content-center">
                                    <div id="nid-preview-wrapper" class="text-white flex-column d-flex text-align-center justify-content-center align-items-center w-100 h-100">
                                        <img id="nid-preview" src="<?php echo esc_url($row['nid_card_image_path'] ?? ''); ?>" alt="Preview" class="w-100 h-100 object-fit-cover" style="display:none;">
                                        <i class="bi bi-person-vcard upload-icon" id="nid-icon"></i>
                                        <span id="nid-text">อัปโหลด</span>
                                        <div class="upload-overlay flex-column d-flex">
                                            <i class="bi bi-person-vcard upload-icon"></i>
                                            <span class="upload-text">อัปโหลด</span>
                                        </div>
                                    </div>
                                    <input type="file" name="nid_card_image" id="nid-card-image" class="upload-input d-none" accept="image/*">
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <p class="text-gray-500">อัปโหลด</p>
                            <h5>รูปภาพบัตรประชาชน</h5>
                            <p>** กรณีอัปโหลดแล้วรูปภาพไม่เปลี่ยนให้ทำการลบรูปภาพก่อน **</p>
                        </div>
                    </div><br>

                    <label for="notes">หมายเหตุ</label>
                    <textarea class="form-control" id="notes" name="notes" placeholder="หมายเหตุ"></textarea><br>

                    <label for="activated-date">เริ่มใช้งานเมื่อ</label>
                    <input type="date" class="form-control" id="activated-date" name="activated_date" placeholder="เริ่มใช้งานเมื่อ"><br>

                    <label for="status">สถานะ</label>
                    <select class="form-select" id="status" name="status" required>
                        <option value="ใช้งาน">ใช้งาน</option>
                        <option value="ยกเลิก">ยกเลิก</option>
                        <option value="เตรียมพร้อม">เตรียมพร้อม</option>
                    </select><br>
                    <button type="submit" class="btn btn-success">บันทึกข้อมูล</button>
                </div>
            </div>
        </div>
    </form>
    <div id="response-message"></div>
<?php
    return ob_get_clean();
}

// ✅ เพิ่ม shortcodes สำหรับ add_bank แต่ละประเภทโดยใช้ loop
$bank_types = ['f789', 's888', 'jnp', 'kaw', 'czo'];

foreach ($bank_types as $type) {
    add_shortcode("add_bank_{$type}", function () use ($type) {
        require_once get_template_directory() . '/includes/permission-handler.php';

        $permissionKey = "can_access_add_bank_{$type}";
        check_permission($permissionKey); // 🔒 ถ้าไม่มีสิทธิ์ → wp_redirect

        return add_bank_form($type); // ✅ ปลอดภัยแล้ว
    });
}

// จัดการข้อมูลที่ส่งมาจากฟอร์มด้วย AJAX
function bam_handle_add_bank_ajax()
{

    require_once get_template_directory() . '/includes/permission-handler.php';
    require_once get_template_directory() . '/includes/image-handler.php';
    require_once get_template_directory() . '/includes/bank-validator.php';
    require_once get_template_directory() . '/includes/encryption.php';

    $bank_type = sanitize_text_field($_POST['bank_type'] ?? '');

    // ✅ ตรวจสอบสิทธิ์การเพิ่มตาม bank_type
    $permissionKey = "can_access_add_bank_{$bank_type}";
    verify_ajax_permission($permissionKey);

    global $wpdb;
    $table = $wpdb->prefix . 'bank_account';

    try {
        // ✅ อ่านค่าจาก POST และทำความสะอาดข้อมูล
        $bank_type      = sanitize_text_field($_POST['bank_type'] ?? '');
        $bank_name      = sanitize_text_field($_POST['bank_name'] ?? '');
        $account_name   = sanitize_text_field($_POST['account_name'] ?? '');
        $bank_number    = preg_replace('/\D/', '', $_POST['bank_number'] ?? '');
        $atm_pin        = preg_replace('/\D/', '', $_POST['atm_pin'] ?? '');
        $app_pin        = preg_replace('/\D/', '', $_POST['app_pin'] ?? '');
        $agent_name     = sanitize_text_field($_POST['agent_name'] ?? '');
        $phone_number   = preg_replace('/\D/', '', $_POST['phone_number'] ?? '');
        $birth_date     = sanitize_text_field($_POST['birth_date'] ?? '');
        $national_id    = preg_replace('/\D/', '', $_POST['national_id'] ?? '');
        $notes          = sanitize_textarea_field($_POST['notes'] ?? '');
        $activated_date = sanitize_text_field($_POST['activated_date'] ?? '');
        $status         = sanitize_text_field($_POST['status'] ?? '');

        // 🔒 ตรวจสอบความถูกต้องด้วย validator
        if (!is_valid_bank_number($bank_number)) {
            throw new Exception("เลขบัญชีไม่ถูกต้อง");
        }
        if ($atm_pin && !is_valid_pin($atm_pin)) {
            throw new Exception("ATM PIN ไม่ถูกต้อง");
        }
        if ($app_pin && !is_valid_pin($app_pin)) {
            throw new Exception("APP PIN ไม่ถูกต้อง");
        }
        if ($national_id && !is_valid_national_id($national_id)) {
            throw new Exception("เลขบัตรประชาชนไม่ถูกต้อง");
        }
        if ($phone_number && !is_valid_phone_number($phone_number)) {
            throw new Exception("เบอร์โทรศัพท์ไม่ถูกต้อง");
        }
        if (!is_valid_status($status)) {
            throw new Exception("สถานะไม่ถูกต้อง");
        }

        // 📷 ตรวจสอบและอัปโหลดรูปภาพ
        $image_url = handle_id_card_upload('nid_card_image');

        // ✅ เข้ารหัสข้อมูลสำคัญก่อน insert
        $account_name_enc = bam_encrypt($account_name);
        $bank_number_enc  = bam_encrypt($bank_number);
        $phone_number_enc = bam_encrypt($phone_number);
        $national_id_enc  = bam_encrypt($national_id);

        // 🧾 เตรียมข้อมูลสำหรับ insert
        $data = [
            'bank_type'       => $bank_type,
            'bank_name'       => $bank_name,
            'account_name'    => $account_name_enc,
            'bank_number'     => $bank_number_enc,
            'atm_pin'         => $atm_pin,
            'app_pin'         => $app_pin,
            'agent_name'      => $agent_name,
            'phone_number'    => $phone_number_enc,
            'birth_date'      => $birth_date,
            'national_id'     => $national_id_enc,
            'notes'           => $notes,
            'activated_date'  => $activated_date,
            'status'          => $status,
            'created_date'    => current_time('mysql'),
        ];

        if ($image_url) {
            $data['nid_card_image_path'] = $image_url;
        }

        // ✂️ กรองค่าว่าง
        $data = array_filter($data, fn($v) => $v !== '');

        // 💾 บันทึกลงฐานข้อมูล
        $result = $wpdb->insert($table, $data);
        if (!$result) {
            throw new Exception("บันทึกไม่สำเร็จ: " . $wpdb->last_error);
        }

        wp_send_json_success([
            'message' => 'บันทึกสำเร็จ',
            'id' => $wpdb->insert_id
        ]);
    } catch (Exception $e) {
        wp_send_json_error(['message' => $e->getMessage()]);
    }
}
add_action('wp_ajax_nopriv_add_bank_ajax', 'bam_handle_add_bank_ajax');
?>