jQuery(function ($) {
    console.log("Core.js is ready.");

    if (!window.bamPermission || !bamPermission.can_access_userrole) {
        showErrorAlert("คุณไม่มีสิทธิ์เข้าถึงหน้านี้");
        return;
    }

    // ฟังก์ชันเริ่มต้นเมื่อโหลดหน้า (INITIAL FUNCTION)
    updateActiveMenu();
    initializeSidebarDropdown();
    
    if (typeof window.initializeBankForm === 'function') {
        window.initializeBankForm();
    }

    // ฟังก์ชันนำทางแบบ AJAX (AJAX Navigation)
    window.navigatePage = function (event, url) {
        if (event && typeof event.preventDefault === 'function') {
            event.preventDefault();
        }

        $.get(url, function (response) {
            const $html = $('<div>').html(response);
            const $newContent = $html.find('#main-content');

            if ($newContent.length > 0) {
                $('#main-content').html($newContent.html());

                // อัปเดต URL โดยไม่ reload
                const cleanUrl = url.split('?')[0];
                history.pushState(null, '', cleanUrl);

                // เรียกฟังก์ชันที่จำเป็น
                updateActiveMenu();
                initializeSidebarDropdown();

                if (typeof window.initializeBankTables === 'function') {
                    window.initializeBankTables();
                }

                if (typeof window.initializeUserroleTables === 'function') {
                    window.initializeUserroleTables();
                }

                if (typeof window.initializeAssistanceTables === 'function') {
                    window.initializeAssistanceTables();
                }
            } else {
                console.warn("ไม่พบ #main-content ใน response จาก: " + url);
            }
        });
    };

    // รองรับการกดปุ่ม Back/Forward
    $(window).on('popstate', function () {
        $.get(location.pathname, function (response) {
            const $html = $('<div>').html(response);
            const $newContent = $html.find('#main-content');

            if ($newContent.length > 0) {
                $('#main-content').html($newContent.html());

                // เรียกฟังก์ชันที่จำเป็น
                updateActiveMenu();
                initializeSidebarDropdown();

                if (typeof window.initializeBankTables === 'function') {
                    window.initializeBankTables();
                }

                if (typeof window.initializeUserroleTables === 'function') {
                    window.initializeUserroleTables();
                }

                if (typeof window.initializeAssistanceTables === 'function') {
                    window.initializeAssistanceTables();
                }
            } else {
                console.warn("ไม่พบ #main-content เมื่อ popstate: " + location.pathname);
            }
        });
    });

    // ฟังก์ชันจัดการเมนูไฮไลต์ (MENU ACTIVE HANDLING)
    function updateActiveMenu() {
        const path = location.pathname;
        $('.nav-link').removeClass('active');

        // Mapping: path keyword → รายชื่อ ID เมนูที่ต้อง active
        const menuMap = {
            '/': ['dashboard'],
            'dashboard': ['dashboard'],
            'f789': ['toggle-menu', 'f789'],
            's888': ['toggle-menu', 's888'],
            'jnp': ['toggle-menu', 'jnp'],
            'kaw': ['toggle-menu', 'kaw'],
            'czo': ['toggle-menu', 'czo'],
            'assistance': ['assistance'],
            'userrole': ['userrole']
        };

        // ตรวจสอบและ active เมนูที่ตรงกับ path
        for (const [slug, menuIds] of Object.entries(menuMap)) {
            if ((slug === '/' && path === '/') || (slug !== '/' && path.includes(slug))) {
                menuIds.forEach(id => $(`#${id}`).addClass('active'));

                break;
            }
        }
    }

    // ค่าคงที่ที่ใช้ทั่วทั้งไฟล์ (CONST CONFIG)
    const animationSpeed = 300;
    const menuOpenClass = 'menu-open';

    // ฟังก์ชันจัดการเมนูดรอปดาวน์ Sidebar (SIDEBAR DROPDOWN)
    function initializeSidebarDropdown() {
        $('[data-my-toggle="treeview"]').off('click').on('click', '.nav-link', function (e) {
            const $link = $(this);
    
            // ตรวจสอบว่าเป็นลิงก์เมนูหลัก (ไม่ใช่เมนูย่อย)
            if ($link.attr('href') === '#') {
                e.preventDefault();
    
                const $menuItem = $link.closest('.nav-item');
                const $submenu = $menuItem.find('.nav-treeview').first();
    
                if ($menuItem.hasClass(menuOpenClass)) {
                    // ถ้าเมนูเปิดอยู่ → ปิดเมนูย่อย และลบคลาส
                    $submenu.stop(true, true).slideUp(animationSpeed);
                    $menuItem.removeClass(menuOpenClass);
                } else {
                    // ถ้าเมนูยังไม่เปิด → เปิดเมนูย่อย และเพิ่มคลาส
                    $submenu.stop(true, true).slideDown(animationSpeed);
                    $menuItem.addClass(menuOpenClass);
                }
            }
        });
    }

    // ฟังก์ชันจัดการเมนูดรอปดาวน์ตาราง (TABLE DROPDOWN)
    $(document).on('click', '.dropdown-item', function (e) {
        e.preventDefault();
        const action = $(this).data('action');
        const row = $(this).closest('tr'); // หรือใช้ data-id ก็ได้

        switch (action) {
            case 'view':
                console.log("ดูข้อมูล", row);
                break;
            case 'edit':
                console.log("แก้ไข", row);
                break;
            case 'deactivate':
                console.log("ยกเลิกใช้งาน", row);
                break;
            case 'delete':
                console.log("ลบ", row);
                break;
        }
    });

    // เรียก navigatePage แบบยกเว้น logout.php
    $(document).on('click', 'a.nav-link', function (e) {
        const href = $(this).attr('href');
        if ($(this).hasClass('btn-logout') || !href || href === '#') return;
        if (e.ctrlKey || e.metaKey || e.which === 2) return;

        e.preventDefault();
        navigatePage(e, href);
    });
});