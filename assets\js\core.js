/**
 * BAM Core JavaScript Module
 * จัดการ AJAX navigation, menu management และ UI interactions หลักของระบบ
 */
(function($) {
    'use strict';

    /**
     * คลาสสำหรับจัดการ Core System
     */
    class BAMCoreManager {
        constructor() {
            // ค่าคงที่สำหรับการจัดการระบบ
            this.config = {
                contentSelector: '#main-content',
                navLinkSelector: '.nav-link',
                dropdownItemSelector: '.dropdown-item',
                animationSpeed: 300,
                menuOpenClass: 'menu-open'
            };

            // รายการฟังก์ชันที่ต้องเรียกหลังจากโหลดหน้าใหม่
            this.initializeFunctions = [
                'initializeBankTables',
                'initializeUserroleTables',
                'initializeAssistanceTables',
                'initializeBankForm'
            ];

            // Menu mapping สำหรับ active states
            this.menuMap = {
                '/': ['dashboard'],
                'dashboard': ['dashboard'],
                'f789': ['toggle-menu', 'f789'],
                's888': ['toggle-menu', 's888'],
                'jnp': ['toggle-menu', 'jnp'],
                'kaw': ['toggle-menu', 'kaw'],
                'czo': ['toggle-menu', 'czo'],
                'assistance': ['assistance'],
                'userrole': ['userrole']
            };

            // Table actions mapping
            this.tableActions = {
                view: 'ดูข้อมูล',
                edit: 'แก้ไข',
                deactivate: 'ยกเลิกใช้งาน',
                delete: 'ลบ'
            };

            this.init();
        }

        /**
         * เริ่มต้นการทำงานของระบบ
         */
        init() {
            console.log('BAM Core Manager initialized.');

            // ตรวจสอบว่าควรทำงานในหน้านี้หรือไม่
            if (!this.shouldInitialize()) {
                console.log('Core manager skipped for this page.');
                return;
            }

            // ตรวจสอบสิทธิ์การเข้าถึง
            if (!this.checkPermissions()) {
                return;
            }

            // เริ่มต้นฟังก์ชันหลัก
            this.initializeCore();

            // ผูก event handlers
            this.bindEvents();
        }

        /**
         * ตรวจสอบว่าควร initialize core manager หรือไม่
         * @returns {boolean}
         */
        shouldInitialize() {
            const currentPath = window.location.pathname;

            // หน้าที่ไม่ต้องใช้ core manager
            const excludedPages = ['/login', '/logout'];

            // ถ้าเป็นหน้าที่ exclude ไว้ ให้ข้าม
            if (excludedPages.some(page => currentPath.includes(page))) {
                return false;
            }

            // ตรวจสอบว่ามี element ที่จำเป็นสำหรับ core manager หรือไม่
            if (!$(this.config.contentSelector).length) {
                console.log('Main content element not found, skipping core manager.');
                return false;
            }

            return true;
        }

        /**
         * ตรวจสอบสิทธิ์การเข้าถึง
         * @returns {boolean} true ถ้ามีสิทธิ์หรือเป็นหน้าที่ไม่ต้องตรวจสอบ
         */
        checkPermissions() {
            // หน้าที่ไม่ต้องตรวจสอบสิทธิ์
            const publicPages = ['/login', '/logout', '/'];
            const currentPath = window.location.pathname;

            // ถ้าเป็นหน้า public ให้ผ่านได้เลย
            if (this.isPublicPage(currentPath, publicPages)) {
                return true;
            }

            // ตรวจสอบว่ามี bamPermission หรือไม่
            if (!window.bamPermission) {
                // ถ้าไม่มี permission object แสดงว่ายังไม่ได้ login
                // redirect ไปหน้า login แทนการแสดง error
                this.redirectToLogin();
                return false;
            }

            return true;
        }

        /**
         * ตรวจสอบว่าเป็นหน้า public หรือไม่
         * @param {string} currentPath - path ปัจจุบัน
         * @param {Array} publicPages - รายการหน้า public
         * @returns {boolean}
         */
        isPublicPage(currentPath, publicPages) {
            return publicPages.some(page =>
                currentPath === page ||
                currentPath.includes(page)
            );
        }

        /**
         * redirect ไปหน้า login
         */
        redirectToLogin() {
            const loginUrl = '/login';
            if (window.location.pathname !== loginUrl) {
                window.location.href = loginUrl;
            }
        }

        /**
         * เริ่มต้นฟังก์ชันหลักของระบบ
         */
        initializeCore() {
            this.updateActiveMenu();
            this.initializeSidebarDropdown();
            this.callInitializeFunction('initializeBankForm');
        }

        /**
         * ผูก event handlers
         */
        bindEvents() {
            // AJAX Navigation - เฉพาะลิงก์ที่ไม่ใช่ logout
            $(document).on('click', 'a.nav-link:not(.btn-logout)', (e) => this.handleNavLinkClick(e));

            // Browser back/forward
            $(window).on('popstate', () => this.handlePopState());

            // Table dropdown actions
            $(document).on('click', this.config.dropdownItemSelector, (e) => this.handleDropdownAction(e));

            // Sidebar dropdown toggle
            $('[data-my-toggle="treeview"]').off('click').on('click', '.nav-link', (e) => this.handleSidebarToggle(e));
        }

        /**
         * จัดการการคลิกลิงก์นำทาง
         * @param {Event} e - Event object
         */
        handleNavLinkClick(e) {
            const $link = $(e.currentTarget);
            const href = $link.attr('href');

            // ตรวจสอบเงื่อนไขที่ไม่ต้องใช้ AJAX navigation
            if (this.shouldSkipAjaxNavigation($link, href, e)) {
                return;
            }

            e.preventDefault();
            this.navigatePage(e, href);
        }

        /**
         * ตรวจสอบว่าควรข้าม AJAX navigation หรือไม่
         * @param {jQuery} $link - jQuery object ของลิงก์
         * @param {string} href - URL ของลิงก์
         * @param {Event} e - Event object
         * @returns {boolean}
         */
        shouldSkipAjaxNavigation($link, href, e) {
            return $link.hasClass('btn-logout') ||
                   !href ||
                   href === '#' ||
                   e.ctrlKey ||
                   e.metaKey ||
                   e.which === 2;
        }

        /**
         * นำทางไปยังหน้าใหม่ด้วย AJAX
         * @param {Event|null} event - Event object
         * @param {string} url - URL ที่ต้องการนำทางไป
         */
        async navigatePage(event, url) {
            if (event && typeof event.preventDefault === 'function') {
                event.preventDefault();
            }

            try {
                const response = await this.fetchPage(url);
                this.handlePageResponse(response, url);
            } catch (error) {
                this.handleNavigationError(error, url);
            }
        }

        /**
         * ดึงข้อมูลหน้าเว็บ
         * @param {string} url - URL ที่ต้องการดึง
         * @returns {Promise} Promise object
         */
        fetchPage(url) {
            return $.get(url);
        }

        /**
         * จัดการ response จากการดึงหน้าเว็บ
         * @param {string} response - HTML response
         * @param {string} url - URL ที่ดึงมา
         */
        handlePageResponse(response, url) {
            const $newContent = this.extractContent(response);

            if ($newContent && $newContent.length > 0) {
                this.updatePageContent($newContent);
                this.updateBrowserHistory(url);
                this.reinitializeComponents();
            } else {
                console.warn(`ไม่พบ ${this.config.contentSelector} ใน response จาก: ${url}`);
            }
        }

        /**
         * ดึงเนื้อหาจาก HTML response
         * @param {string} response - HTML response
         * @returns {jQuery|null} jQuery object ของเนื้อหาหรือ null
         */
        extractContent(response) {
            try {
                const $html = $('<div>').html(response);
                return $html.find(this.config.contentSelector);
            } catch (error) {
                console.error('Error parsing HTML response:', error);
                return null;
            }
        }

        /**
         * อัปเดตเนื้อหาหน้าเว็บ
         * @param {jQuery} $newContent - เนื้อหาใหม่
         */
        updatePageContent($newContent) {
            $(this.config.contentSelector).html($newContent.html());
        }

        /**
         * อัปเดต browser history
         * @param {string} url - URL ที่ต้องการอัปเดต
         */
        updateBrowserHistory(url) {
            const cleanUrl = this.sanitizeUrl(url);
            history.pushState(null, '', cleanUrl);
        }

        /**
         * ทำความสะอาด URL
         * @param {string} url - URL ที่ต้องทำความสะอาด
         * @returns {string} URL ที่ทำความสะอาดแล้ว
         */
        sanitizeUrl(url) {
            return url.split('?')[0];
        }

        /**
         * เรียกใช้ components ใหม่หลังจากโหลดหน้า
         */
        reinitializeComponents() {
            this.updateActiveMenu();
            this.initializeSidebarDropdown();
            this.callAllInitializeFunctions();
        }

        /**
         * เรียกใช้ฟังก์ชัน initialize ทั้งหมด
         */
        callAllInitializeFunctions() {
            this.initializeFunctions.forEach(funcName => {
                this.callInitializeFunction(funcName);
            });
        }

        /**
         * เรียกใช้ฟังก์ชัน initialize เฉพาะ
         * @param {string} functionName - ชื่อฟังก์ชัน
         */
        callInitializeFunction(functionName) {
            if (typeof window[functionName] === 'function') {
                try {
                    window[functionName]();
                } catch (error) {
                    console.error(`Error calling ${functionName}:`, error);
                }
            }
        }

        /**
         * จัดการ browser back/forward
         */
        async handlePopState() {
            try {
                const response = await this.fetchPage(location.pathname);
                this.handlePopStateResponse(response);
            } catch (error) {
                this.handleNavigationError(error, location.pathname);
            }
        }

        /**
         * จัดการ response จาก popstate
         * @param {string} response - HTML response
         */
        handlePopStateResponse(response) {
            const $newContent = this.extractContent(response);

            if ($newContent && $newContent.length > 0) {
                this.updatePageContent($newContent);
                this.reinitializeComponents();
            } else {
                console.warn(`ไม่พบ ${this.config.contentSelector} เมื่อ popstate: ${location.pathname}`);
            }
        }

        /**
         * จัดการข้อผิดพลาดในการนำทาง
         * @param {Error} error - Error object
         * @param {string} url - URL ที่เกิดข้อผิดพลาด
         */
        handleNavigationError(error, url) {
            console.error(`Navigation error for ${url}:`, error);
            this.showError(`ไม่สามารถโหลดหน้า ${url} ได้`);
        }

        /**
         * อัปเดตเมนูที่ active ตาม URL ปัจจุบัน
         */
        updateActiveMenu() {
            const path = location.pathname;
            $(this.config.navLinkSelector).removeClass('active');

            // ตรวจสอบและ active เมนูที่ตรงกับ path
            for (const [slug, menuIds] of Object.entries(this.menuMap)) {
                if (this.isPathMatch(slug, path)) {
                    menuIds.forEach(id => $(`#${id}`).addClass('active'));
                    break;
                }
            }
        }

        /**
         * ตรวจสอบว่า path ตรงกับ slug หรือไม่
         * @param {string} slug - slug ที่ต้องตรวจสอบ
         * @param {string} path - path ปัจจุบัน
         * @returns {boolean}
         */
        isPathMatch(slug, path) {
            return (slug === '/' && path === '/') ||
                   (slug !== '/' && path.includes(slug));
        }

        /**
         * เริ่มต้นการทำงานของ sidebar dropdown
         */
        initializeSidebarDropdown() {
            $('[data-my-toggle="treeview"]').off('click').on('click', '.nav-link', (e) => {
                this.handleSidebarToggle(e);
            });
        }

        /**
         * จัดการการ toggle sidebar menu
         * @param {Event} e - Event object
         */
        handleSidebarToggle(e) {
            const $link = $(e.currentTarget);

            // ตรวจสอบว่าเป็นลิงก์เมนูหลัก (ไม่ใช่เมนูย่อย)
            if ($link.attr('href') === '#') {
                e.preventDefault();

                const $menuItem = $link.closest('.nav-item');
                const $submenu = $menuItem.find('.nav-treeview').first();

                this.toggleSubmenu($menuItem, $submenu);
            }
        }

        /**
         * Toggle submenu
         * @param {jQuery} $menuItem - Menu item element
         * @param {jQuery} $submenu - Submenu element
         */
        toggleSubmenu($menuItem, $submenu) {
            if ($menuItem.hasClass(this.config.menuOpenClass)) {
                // ปิดเมนูย่อย
                $submenu.stop(true, true).slideUp(this.config.animationSpeed);
                $menuItem.removeClass(this.config.menuOpenClass);
            } else {
                // เปิดเมนูย่อย
                $submenu.stop(true, true).slideDown(this.config.animationSpeed);
                $menuItem.addClass(this.config.menuOpenClass);
            }
        }

        /**
         * จัดการ dropdown actions ในตาราง
         * @param {Event} e - Event object
         */
        handleDropdownAction(e) {
            e.preventDefault();

            const $item = $(e.currentTarget);
            const action = $item.data('action');
            const itemId = $item.data('id');
            const $row = $item.closest('tr');

            this.executeTableAction(action, itemId, $row);
        }

        /**
         * ดำเนินการ action ในตาราง
         * @param {string} action - การกระทำ
         * @param {number} itemId - ID ของรายการ
         * @param {jQuery} $row - แถวในตาราง
         */
        executeTableAction(action, itemId, $row) {
            const actionName = this.tableActions[action] || action;

            console.log(`${actionName}:`, {
                action: action,
                id: itemId,
                row: $row
            });

            // สามารถเพิ่ม logic เฉพาะแต่ละ action ได้ที่นี่
            switch (action) {
                case 'view':
                    this.handleViewAction(itemId, $row);
                    break;
                case 'edit':
                    this.handleEditAction(itemId, $row);
                    break;
                case 'deactivate':
                    this.handleDeactivateAction(itemId, $row);
                    break;
                case 'delete':
                    this.handleDeleteAction(itemId, $row);
                    break;
                default:
                    console.warn(`Unknown action: ${action}`);
            }
        }

        /**
         * จัดการการดูข้อมูล
         * @param {number} itemId - ID ของรายการ
         * @param {jQuery} $row - แถวในตาราง
         */
        handleViewAction(itemId, $row) {
            // Implementation สำหรับการดูข้อมูล
            console.log('View action:', itemId);
        }

        /**
         * จัดการการแก้ไขข้อมูล
         * @param {number} itemId - ID ของรายการ
         * @param {jQuery} $row - แถวในตาราง
         */
        handleEditAction(itemId, $row) {
            // Implementation สำหรับการแก้ไขข้อมูล
            console.log('Edit action:', itemId);
        }

        /**
         * จัดการการยกเลิกใช้งาน
         * @param {number} itemId - ID ของรายการ
         * @param {jQuery} $row - แถวในตาราง
         */
        handleDeactivateAction(itemId, $row) {
            // Implementation สำหรับการยกเลิกใช้งาน
            console.log('Deactivate action:', itemId);
        }

        /**
         * จัดการการลบข้อมูล
         * @param {number} itemId - ID ของรายการ
         * @param {jQuery} $row - แถวในตาราง
         */
        handleDeleteAction(itemId, $row) {
            // Implementation สำหรับการลบข้อมูล
            console.log('Delete action:', itemId);
        }

        /**
         * แสดงข้อความข้อผิดพลาด
         * @param {string} message - ข้อความข้อผิดพลาด
         */
        showError(message) {
            if (typeof showErrorAlert === 'function') {
                showErrorAlert(message);
            } else {
                console.error(message);
                alert(message);
            }
        }

        /**
         * แสดงข้อความสำเร็จ
         * @param {string} message - ข้อความสำเร็จ
         */
        showSuccess(message) {
            if (typeof showSuccessAlert === 'function') {
                showSuccessAlert(message);
            } else {
                console.log(message);
            }
        }
    }

    /**
     * เริ่มต้นโมดูลเมื่อ DOM พร้อม
     */
    $(function() {
        // สร้าง instance ของ BAMCoreManager
        const coreManager = new BAMCoreManager();

        // เก็บ reference ไว้ใน window object สำหรับการเข้าถึงจากภายนอก
        window.bamCoreManager = coreManager;

        // รักษาความเข้ากันได้กับโค้ดเดิม
        window.navigatePage = function(event, url) {
            coreManager.navigatePage(event, url);
        };

        window.updateActiveMenu = function() {
            coreManager.updateActiveMenu();
        };

        window.initializeSidebarDropdown = function() {
            coreManager.initializeSidebarDropdown();
        };
    });

})(jQuery);