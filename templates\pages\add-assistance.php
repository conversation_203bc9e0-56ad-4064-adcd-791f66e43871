<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

add_shortcode('bam_add_assistance_form', function () {
    require_once get_template_directory() . '/includes/permission-handler.php';
    check_permission('can_access_add_assistance');

    ob_start();

    global $wpdb;
    $roles = $wpdb->get_results("SELECT id, role_name FROM {$wpdb->prefix}userrole ORDER BY role_name ASC");
?>
    <form id="add-assistance-form">
        <div class="row">
            <div class="col-12 card p-3">
                <label for="username" class="form-label">ชื่อผู้ใช้</label>
                <input type="text" class="form-control" id="username" name="username" required><br>

                <label for="password" class="form-label">รหัสผ่าน</label>
                <input type="password" class="form-control" id="password" name="password" required><br>

                <label for="role_id" class="form-label">สิทธิ์</label>
                <select class="form-select" id="role_id" name="role_id" required><br>
                    <option value="">-- เลือกสิทธิ์ --</option>
                    <?php foreach ($roles as $role): ?>
                        <option value="<?php echo esc_attr($role->id); ?>">
                            <?php echo esc_html($role->role_name); ?>
                        </option>
                    <?php endforeach; ?>
                </select><br>

                <button type="submit" class="btn btn-primary">บันทึก</button>
            </div>
        </div>
    </form>
<?php
    return ob_get_clean();
});

// จัดการข้อมูลที่ส่งมาจากฟอร์มด้วย AJAX
function bam_handle_add_assistance_ajax()
{

    require_once get_template_directory() . '/includes/permission-handler.php';

    header('Content-Type: application/json');
    session_start();

    // 🔒 บล็อก AJAX ถ้าไม่มีสิทธิ์
    verify_ajax_permission('can_access_add_assistance');

    global $wpdb;
    $table = $wpdb->prefix . 'assistance';

    $username = sanitize_user($_POST['username']);
    $password = $_POST['password'];
    $role_id  = intval($_POST['role_id']);

    // ตรวจ username ซ้ำ
    $exists = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table WHERE username = %s", $username));
    if ($exists > 0) {
        wp_send_json_error(['message' => 'ชื่อผู้ใช้นี้มีอยู่แล้วในระบบ']);
    }

    // hash password
    $hashed = password_hash($password, PASSWORD_DEFAULT);

    $inserted = $wpdb->insert($table, [
        'username' => $username,
        'password_hash' => $hashed,
        'role_id' => $role_id,
        'is_active' => 1,
        'created_at' => current_time('mysql')
    ]);

    if ($inserted) {
        wp_send_json_success(['message' => 'เพิ่มผู้ช่วยเรียบร้อยแล้ว']);
    } else {
        wp_send_json_error(['message' => 'ไม่สามารถเพิ่มข้อมูลได้']);
    }
}
add_action('wp_ajax_nopriv_add_assistance_ajax', 'bam_handle_add_assistance_ajax');
?>