<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

function bam_render_login_form()
{
    ob_start();
?>
    <div class="container-fluid min-vh-100 d-flex justify-content-center align-items-center bg-body-tertiary">
        <div class="row bg-white rounded-4 shadow w-100 overflow-hidden" style="max-width: 900px;">

            <!-- คอลัมน์: Login -->
            <div class="col-md-6 pt-4 pb-3 px-4">
                <div class="d-flex align-items-center justify-content-start pb-3 text-nowrap border-bottom">
                    <div class="brand-link d-flex align-items-center text-decoration-none">
                        <img src="https://yt3.ggpht.com/yti/ANjgQV8vZutCTvT9vmnGFYpOcP7-p_jw7lGaLC-koLXb3UiNnA=s88-c-k-c0x00ffffff-no-rj" alt="AdminLTE Logo" class="brand-image-login rounded-circle shadow float-start border border-primary">
                        <span class="c-brand-text fw-bold text-black ms-2 d-md-inline fs-7">BAM SYSTEM</span>
                    </div>
                </div>
                <div class="my-4">
                    <h3 class="fw-bold">เข้าสู่ระบบ</h3>
                    <span class="text-gray-500">Bank Account Management System</span>
                </div>
                <form id="bam-login-form" method="POST" action="<?php echo admin_url('admin-post.php'); ?>">
                    <input type="hidden" name="action" value="bam_user_login">
                    <?php wp_nonce_field('bam_login_action', 'bam_login_nonce'); ?>

                    <label for="username" class="form-label">ชื่อผู้ใช้</label>
                    <input type="text" name="username" class="form-control" placeholder="ชื่อผู้ใช้" required><br>

                    <label for="password" class="form-label">รหัสผ่าน</label>
                    <input type="password" name="password" class="form-control" placeholder="รหัสผ่าน" required><br>

                    <div class="error-alert"><!-- alert จะแสดงตรงนี้ --></div>
                    
                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary w-100">เข้าสู่ระบบ</button>
                    </div>

                    <div class="version-line-wrapper text-center small text-gray-500 mt-4">
                        <span class="line"></span><?= get_bam_version() ?><span class="line"></span>
                    </div>
                </form>
            </div>

            <!-- คอลัมน์: แจ้งเตือน -->
            <div class="col-md-6 p-0 position-relative">
                <img src="https://bam-sys.com/wp-content/uploads/2025/06/img-login-1.png" alt="เข้าสู่ระบบ BAM" class="img-fluid w-100 h-100 object-fit-cover position-absolute top-0 start-0" />

                <div class="position-relative text-white p-4 z-1">
                    <h3 class="fw-bold text-shadow">แจ้งเตือน</h3>
                    <div class="bg-blur p-3 rounded-3 text-black">
                        <div class="position-absolute top-0 end-0 me-2 mt-1 small text-muted">
                            <p>26/05/2025</p>
                        </div>
                        <div class="row align-items-center">
                            <div class="col-md-1 col-1 me-2 fs-3">
                                <i class="bi bi-key-fill"></i>
                            </div>
                            <div class="col-md-10 col-9">
                                <span><strong>แจ้งเตือนความปลอดภัย</strong><br>ไม่ควรบันทึกรหัสผ่านผู้ใช้งานของท่านไว้บนบราวเซอร์ทุกกรณี</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php
    return ob_get_clean();
}
add_shortcode('bam_login_form', 'bam_render_login_form');

function bam_user_login_handler()
{
    require_once get_template_directory() . '/includes/permission-handler.php';

    header('Content-Type: application/json');
    session_start();
    global $wpdb;

    define('MAX_LOGIN_ATTEMPTS', 3);    // จำนวนครั้งสูงสุด
    define('LOCKOUT_TIME', 300);        // ระยะเวลาบล็อก (วินาที)

    // ✅ ตรวจสอบ nonce ป้องกัน CSRF
    if (!isset($_POST['bam_login_nonce']) || !wp_verify_nonce($_POST['bam_login_nonce'], 'bam_login_action')) {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถยืนยันความถูกต้องของแบบฟอร์มได้']);
        exit;
    }

    // ✅ ตรวจสอบว่าบัญชีโดนบล็อกหรือไม่
    if (isset($_SESSION['login_attempts']) && $_SESSION['login_attempts'] >= MAX_LOGIN_ATTEMPTS) {
        $timeSinceLast = time() - ($_SESSION['last_attempt_time'] ?? 0);
        if ($timeSinceLast < LOCKOUT_TIME) {
            $remaining = LOCKOUT_TIME - $timeSinceLast;
            echo json_encode(['success' => false, 'data' => ['message' => "กรุณาลองใหม่อีกครั้งในอีก $remaining วินาที"]]);
            exit;
        } else {
            // รีเซ็ตถ้าครบเวลาแล้ว
            $_SESSION['login_attempts'] = 0;
            $_SESSION['last_attempt_time'] = 0;
        }
    }

    // ✅ ตรวจสอบข้อมูล login
    $table = $wpdb->prefix . 'assistance';
    $username = sanitize_text_field($_POST['username']);
    $password = sanitize_text_field($_POST['password']);

    $user = $wpdb->get_row(
        $wpdb->prepare("SELECT * FROM $table WHERE username = %s AND is_active = 1", $username),
        ARRAY_A
    );

    // ✅ ตรวจสอบรหัสผ่าน
    if ($user && password_verify($password, $user['password_hash'])) {
        // เข้าระบบสำเร็จ → เคลียร์ session
        $_SESSION['bam_user_id'] = $user['id'];
        $_SESSION['bam_username'] = $user['username'];
        $_SESSION['bam_role_id'] = $user['role_id'];

        set_user_permission_to_session($user['role_id']);

        $_SESSION['login_attempts'] = 0;
        $_SESSION['last_attempt_time'] = 0;

        $wpdb->update($table, ['last_login' => current_time('mysql')], ['id' => $user['id']]);

        wp_send_json_success(['redirect' => home_url('/dashboard')]);
        exit;
    } else {
        // ผิด → บันทึก attempt
        $_SESSION['login_attempts'] = ($_SESSION['login_attempts'] ?? 0) + 1;
        $_SESSION['last_attempt_time'] = time();

        echo json_encode(['success' => false, 'message' => 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง']);
        exit;
    }
}
add_action('admin_post_nopriv_bam_user_login', 'bam_user_login_handler');
?>