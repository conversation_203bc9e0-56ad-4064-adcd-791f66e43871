<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

// ค่าคงที่สำหรับการจำกัดการเข้าสู่ระบบ
define('BAM_MAX_LOGIN_ATTEMPTS', 3);
define('BAM_LOCKOUT_TIME', 300); // 5 นาที
define('BAM_NONCE_ACTION', 'bam_login_action');
define('BAM_NONCE_NAME', 'bam_login_nonce');
/**
 * แสดงส่วนหัวแบรนด์
 * @return string HTML ของ brand header
 */
function bam_render_brand_header()
{
    return '
    <div class="d-flex align-items-center justify-content-start pb-3 text-nowrap border-bottom">
        <div class="brand-link d-flex align-items-center text-decoration-none">
            <img src="https://yt3.ggpht.com/yti/ANjgQV8vZutCTvT9vmnGFYpOcP7-p_jw7lGaLC-koLXb3UiNnA=s88-c-k-c0x00ffffff-no-rj"
                 alt="AdminLTE Logo"
                 class="brand-image-login rounded-circle shadow float-start border border-primary">
            <span class="c-brand-text fw-bold text-black ms-2 d-md-inline fs-7">BAM SYSTEM</span>
        </div>
    </div>';
}

/**
 * แสดงหัวข้อการเข้าสู่ระบบ
 * @return string HTML ของ login title
 */
function bam_render_login_title()
{
    return '
    <div class="my-4">
        <h3 class="fw-bold">เข้าสู่ระบบ</h3>
        <span class="text-gray-500">Bank Account Management System</span>
    </div>';
}

/**
 * แสดงฟิลด์ฟอร์มเข้าสู่ระบบ
 * @return string HTML ของฟอร์มฟิลด์
 */
function bam_render_login_form_fields()
{
    ob_start();
    ?>
    <form id="bam-login-form" method="POST" action="<?php echo esc_url(admin_url('admin-post.php')); ?>">
        <input type="hidden" name="action" value="bam_user_login">
        <?php wp_nonce_field(BAM_NONCE_ACTION, BAM_NONCE_NAME); ?>

        <label for="username" class="form-label">ชื่อผู้ใช้</label>
        <input type="text" name="username" id="username" class="form-control" placeholder="ชื่อผู้ใช้" required><br>

        <label for="password" class="form-label">รหัสผ่าน</label>
        <input type="password" name="password" id="password" class="form-control" placeholder="รหัสผ่าน" required><br>

        <div class="error-alert"><!-- alert จะแสดงตรงนี้ --></div>

        <div class="mt-4">
            <button type="submit" class="btn btn-primary w-100">เข้าสู่ระบบ</button>
        </div>

        <div class="version-line-wrapper text-center small text-gray-500 mt-4">
            <span class="line"></span><?= esc_html(get_bam_version()) ?><span class="line"></span>
        </div>
    </form>
    <?php
    return ob_get_clean();
}

/**
 * แสดงคอลัมน์แจ้งเตือน
 * @return string HTML ของคอลัมน์แจ้งเตือน
 */
function bam_render_notification_column()
{
    return '
    <!-- คอลัมน์: แจ้งเตือน -->
    <div class="col-md-6 p-0 position-relative">
        <img src="https://bam-sys.com/wp-content/uploads/2025/06/img-login-1.png"
             alt="เข้าสู่ระบบ BAM"
             class="img-fluid w-100 h-100 object-fit-cover position-absolute top-0 start-0" />

        <div class="position-relative text-white p-4 z-1">
            <h3 class="fw-bold text-shadow">แจ้งเตือน</h3>
            <div class="bg-blur p-3 rounded-3 text-black">
                <div class="position-absolute top-0 end-0 me-2 mt-1 small text-muted">
                    <p>' . esc_html(date('d/m/Y')) . '</p>
                </div>
                <div class="row align-items-center">
                    <div class="col-md-1 col-1 me-2 fs-3">
                        <i class="bi bi-key-fill"></i>
                    </div>
                    <div class="col-md-10 col-9">
                        <span><strong>แจ้งเตือนความปลอดภัย</strong><br>ไม่ควรบันทึกรหัสผ่านผู้ใช้งานของท่านไว้บนบราวเซอร์ทุกกรณี</span>
                    </div>
                </div>
            </div>
        </div>
    </div>';
}

/**
 * แสดงคอลัมน์ฟอร์มเข้าสู่ระบบ
 * @return string HTML ของคอลัมน์ login
 */
function bam_render_login_column()
{
    ob_start();
    ?>
    <!-- คอลัมน์: Login -->
    <div class="col-md-6 pt-4 pb-3 px-4">
        <?php echo bam_render_brand_header(); ?>
        <?php echo bam_render_login_title(); ?>
        <?php echo bam_render_login_form_fields(); ?>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * ฟังก์ชันสำหรับแสดงฟอร์ม login (shortcode)
 * @return string HTML ของฟอร์ม login
 */
function bam_render_login_form()
{
    ob_start();
    ?>
    <div class="container-fluid min-vh-100 d-flex justify-content-center align-items-center bg-body-tertiary">
        <div class="row bg-white rounded-4 shadow w-100 overflow-hidden" style="max-width: 900px;">
            <?php echo bam_render_login_column(); ?>
            <?php echo bam_render_notification_column(); ?>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('bam_login_form', 'bam_render_login_form');

/**
 * จัดการการเข้าสู่ระบบ
 * ตรวจสอบข้อมูลผู้ใช้และจำกัดจำนวนครั้งในการเข้าสู่ระบบ
 */
function bam_handle_user_login()
{
    // เริ่มต้นการตั้งค่าและตรวจสอบความปลอดภัย
    bam_initialize_login_handler();

    // ตรวจสอบ CSRF token
    if (!bam_verify_nonce()) {
        bam_send_error_response('ไม่สามารถยืนยันความถูกต้องของแบบฟอร์มได้');
    }

    // ตรวจสอบการบล็อกบัญชี
    if (bam_is_account_locked()) {
        return; // ฟังก์ชันจะส่ง response และ exit เอง
    }

    // ตรวจสอบข้อมูลการเข้าสู่ระบบ
    $credentials = bam_sanitize_login_credentials();
    $user = bam_get_user_by_username($credentials['username']);

    // ตรวจสอบรหัสผ่านและดำเนินการตามผลลัพธ์
    if (bam_verify_password($user, $credentials['password'])) {
        bam_handle_successful_login($user);
    } else {
        bam_handle_failed_login();
    }
}

/**
 * เริ่มต้นการตั้งค่าสำหรับ login handler
 */
function bam_initialize_login_handler()
{
    require_once get_template_directory() . '/includes/permission-handler.php';

    if (!headers_sent()) {
        header('Content-Type: application/json');
    }

    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
}

/**
 * ตรวจสอบ CSRF nonce
 * @return bool true ถ้า nonce ถูกต้อง
 */
function bam_verify_nonce()
{
    return isset($_POST[BAM_NONCE_NAME]) &&
           wp_verify_nonce($_POST[BAM_NONCE_NAME], BAM_NONCE_ACTION);
}

/**
 * ตรวจสอบว่าบัญชีถูกบล็อกหรือไม่
 * @return bool true ถ้าบัญชีถูกบล็อก
 */
function bam_is_account_locked()
{
    $attempts = isset($_SESSION['login_attempts']) ? $_SESSION['login_attempts'] : 0;

    if ($attempts >= BAM_MAX_LOGIN_ATTEMPTS) {
        $last_attempt = isset($_SESSION['last_attempt_time']) ? $_SESSION['last_attempt_time'] : 0;
        $time_since_last = time() - $last_attempt;

        if ($time_since_last < BAM_LOCKOUT_TIME) {
            $remaining = BAM_LOCKOUT_TIME - $time_since_last;
            bam_send_error_response("กรุณาลองใหม่อีกครั้งในอีก {$remaining} วินาที");
            return true;
        } else {
            // รีเซ็ตถ้าครบเวลาแล้ว
            bam_reset_login_attempts();
        }
    }

    return false;
}

/**
 * ทำความสะอาดข้อมูลการเข้าสู่ระบบ
 * @return array ข้อมูลที่ทำความสะอาดแล้ว
 */
function bam_sanitize_login_credentials()
{
    $username = isset($_POST['username']) ? $_POST['username'] : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';

    return array(
        'username' => sanitize_user($username),
        'password' => $password // ไม่ sanitize password เพื่อรักษาความถูกต้อง
    );
}

/**
 * ดึงข้อมูลผู้ใช้จากฐานข้อมูล
 * @param string $username ชื่อผู้ใช้
 * @return array|null ข้อมูลผู้ใช้หรือ null ถ้าไม่พบ
 */
function bam_get_user_by_username($username)
{
    global $wpdb;
    $table = $wpdb->prefix . 'assistance';

    $user = $wpdb->get_row(
        $wpdb->prepare("SELECT * FROM $table WHERE username = %s AND is_active = 1", $username),
        'ARRAY_A'
    );

    return $user ? $user : null;
}

/**
 * ตรวจสอบรหัสผ่าน
 * @param array|null $user ข้อมูลผู้ใช้
 * @param string $password รหัสผ่านที่ป้อน
 * @return bool true ถ้ารหัสผ่านถูกต้อง
 */
function bam_verify_password($user, $password)
{
    return $user && password_verify($password, $user['password_hash']);
}

/**
 * จัดการเมื่อเข้าสู่ระบบสำเร็จ
 * @param array $user ข้อมูลผู้ใช้
 */
function bam_handle_successful_login($user)
{
    // ตั้งค่า session สำหรับผู้ใช้
    $_SESSION['bam_user_id'] = $user['id'];
    $_SESSION['bam_username'] = $user['username'];
    $_SESSION['bam_role_id'] = $user['role_id'];

    // ตั้งค่าสิทธิ์ผู้ใช้
    set_user_permission_to_session($user['role_id']);

    // รีเซ็ตการนับจำนวนครั้งในการเข้าสู่ระบบ
    bam_reset_login_attempts();

    // อัปเดตเวลาเข้าสู่ระบบล่าสุด
    bam_update_last_login($user['id']);

    // ส่งผลลัพธ์สำเร็จ
    wp_send_json_success(array('redirect' => home_url('/dashboard')));
}

/**
 * จัดการเมื่อเข้าสู่ระบบไม่สำเร็จ
 */
function bam_handle_failed_login()
{
    // เพิ่มจำนวนครั้งในการพยายามเข้าสู่ระบบ
    $attempts = isset($_SESSION['login_attempts']) ? $_SESSION['login_attempts'] : 0;
    $_SESSION['login_attempts'] = $attempts + 1;
    $_SESSION['last_attempt_time'] = time();

    bam_send_error_response('ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง');
}

/**
 * รีเซ็ตการนับจำนวนครั้งในการเข้าสู่ระบบ
 */
function bam_reset_login_attempts()
{
    $_SESSION['login_attempts'] = 0;
    $_SESSION['last_attempt_time'] = 0;
}

/**
 * อัปเดตเวลาเข้าสู่ระบบล่าสุด
 * @param int $user_id ID ของผู้ใช้
 */
function bam_update_last_login($user_id)
{
    global $wpdb;
    $table = $wpdb->prefix . 'assistance';

    $wpdb->update(
        $table,
        array('last_login' => current_time('mysql')),
        array('id' => $user_id),
        array('%s'),
        array('%d')
    );
}

/**
 * ส่งข้อความผิดพลาดและหยุดการทำงาน
 * @param string $message ข้อความผิดพลาด
 */
function bam_send_error_response($message)
{
    wp_send_json_error(array('message' => $message));
}

/**
 * ฟังก์ชันสำหรับจัดการการเข้าสู่ระบบ (WordPress action)
 */
function bam_user_login_handler()
{
    bam_handle_user_login();
}
add_action('admin_post_nopriv_bam_user_login', 'bam_user_login_handler');
?>