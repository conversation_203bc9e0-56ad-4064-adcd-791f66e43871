<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าถึงโดยตรง

/**
 * BAM Permission Handler
 * คลาสสำหรับจัดการสิทธิ์การเข้าถึงในระบบ BAM
 * รวมการตรวจสอบสิทธิ์, การจัดการ session และการตรวจสอบ AJAX
 */
class BAM_Permission_Handler
{
    // ค่าคงที่สำหรับการจัดการสิทธิ์
    private const SESSION_KEY = 'bam_permission';
    private const USER_ID_KEY = 'bam_user_id';
    private const ROLE_ID_KEY = 'bam_role_id';
    private const USERNAME_KEY = 'bam_username';

    // ค่าคงที่สำหรับ nonce
    private const NONCE_ACTION = 'bam_nonce_action';
    private const DEFAULT_REDIRECT_URL = '/dashboard';

    // ฟิลด์ที่ไม่ใช่สิทธิ์ในตาราง userrole
    private const NON_PERMISSION_FIELDS = array('id', 'role_name', 'created_at');

    /**
     * เริ่มต้น session หากยังไม่ได้เริ่ม
     */
    public static function ensure_session_started()
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    /**
     * ตรวจสอบว่าผู้ใช้ล็อกอินแล้วหรือไม่
     * @return bool true ถ้าล็อกอินแล้ว
     */
    public static function is_user_logged_in()
    {
        self::ensure_session_started();
        return isset($_SESSION[self::USER_ID_KEY]) && !empty($_SESSION[self::USER_ID_KEY]);
    }

    /**
     * ดึงข้อมูลผู้ใช้ปัจจุบันจาก session
     * @return array|null ข้อมูลผู้ใช้หรือ null ถ้าไม่ได้ล็อกอิน
     */
    public static function get_current_user()
    {
        if (!self::is_user_logged_in()) {
            return null;
        }

        return array(
            'id' => $_SESSION[self::USER_ID_KEY],
            'username' => $_SESSION[self::USERNAME_KEY] ?? '',
            'role_id' => $_SESSION[self::ROLE_ID_KEY] ?? 0
        );
    }

    /**
     * ดึงสิทธิ์ตาม role_id จากฐานข้อมูล
     * @param int $role_id ID ของ role
     * @return array สิทธิ์ทั้งหมดของ role
     */
    public static function get_user_permission($role_id)
    {
        // ตรวจสอบและ sanitize input
        $role_id = self::sanitize_role_id($role_id);
        if (!$role_id) {
            return array();
        }

        // ดึงข้อมูลจากฐานข้อมูล
        $permission_data = self::fetch_permission_from_database($role_id);

        // กรองเฉพาะฟิลด์สิทธิ์
        return self::filter_permission_fields($permission_data);
    }

    /**
     * ตรวจสอบและทำความสะอาด role_id
     * @param mixed $role_id ID ของ role
     * @return int|false role_id ที่ถูกต้องหรือ false
     */
    private static function sanitize_role_id($role_id)
    {
        $sanitized = filter_var($role_id, FILTER_VALIDATE_INT, array(
            'options' => array('min_range' => 1)
        ));

        return $sanitized !== false ? $sanitized : false;
    }

    /**
     * ดึงข้อมูลสิทธิ์จากฐานข้อมูล
     * @param int $role_id ID ของ role
     * @return array|null ข้อมูลสิทธิ์หรือ null
     */
    private static function fetch_permission_from_database($role_id)
    {
        global $wpdb;
        $table = $wpdb->prefix . 'userrole';

        $permission = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table WHERE id = %d LIMIT 1", $role_id),
            'ARRAY_A'
        );

        return $permission ?: array();
    }

    /**
     * กรองเฉพาะฟิลด์สิทธิ์ (ตัดฟิลด์ที่ไม่ใช่สิทธิ์ออก)
     * @param array $permission_data ข้อมูลสิทธิ์ทั้งหมด
     * @return array ข้อมูลสิทธิ์ที่กรองแล้ว
     */
    private static function filter_permission_fields($permission_data)
    {
        if (empty($permission_data)) {
            return array();
        }

        // ลบฟิลด์ที่ไม่ใช่สิทธิ์
        foreach (self::NON_PERMISSION_FIELDS as $field) {
            unset($permission_data[$field]);
        }

        return $permission_data;
    }

    /**
     * ตรวจสอบสิทธิ์การเข้าถึง
     * @param string $permission_key คีย์สิทธิ์ที่ต้องตรวจสอบ
     * @param string $redirect_url URL สำหรับ redirect เมื่อไม่มีสิทธิ์
     */
    public static function check_permission($permission_key, $redirect_url = null)
    {
        // ตรวจสอบและ sanitize input
        $permission_key = self::sanitize_permission_key($permission_key);
        if (!$permission_key) {
            self::handle_permission_denied($redirect_url);
        }

        // เริ่มต้น session
        self::ensure_session_started();

        // ตรวจสอบสิทธิ์
        if (!self::has_permission($permission_key)) {
            self::handle_permission_denied($redirect_url);
        }
    }

    /**
     * ตรวจสอบว่ามีสิทธิ์หรือไม่
     * @param string $permission_key คีย์สิทธิ์
     * @return bool true ถ้ามีสิทธิ์
     */
    public static function has_permission($permission_key)
    {
        self::ensure_session_started();

        return isset($_SESSION[self::SESSION_KEY][$permission_key]) &&
               intval($_SESSION[self::SESSION_KEY][$permission_key]) === 1;
    }

    /**
     * ตรวจสอบและทำความสะอาด permission key
     * @param string $permission_key คีย์สิทธิ์
     * @return string|false permission key ที่ถูกต้องหรือ false
     */
    private static function sanitize_permission_key($permission_key)
    {
        if (!is_string($permission_key) || empty($permission_key)) {
            return false;
        }

        // ตรวจสอบรูปแบบ permission key (ควรเป็น can_access_xxx หรือ can_xxx)
        if (!preg_match('/^can_[a-z0-9_]+$/', $permission_key)) {
            return false;
        }

        return sanitize_key($permission_key);
    }

    /**
     * จัดการเมื่อไม่มีสิทธิ์เข้าถึง
     * @param string|null $redirect_url URL สำหรับ redirect
     */
    private static function handle_permission_denied($redirect_url = null)
    {
        $redirect_url = $redirect_url ?: home_url(self::DEFAULT_REDIRECT_URL);

        // ป้องกัน headers already sent
        if (!headers_sent()) {
            wp_redirect($redirect_url);
            exit;
        } else {
            // ใช้ JavaScript redirect หาก headers ถูกส่งแล้ว
            printf(
                '<script>window.location.href = "%s";</script>',
                esc_url($redirect_url)
            );
            exit;
        }
    }

    /**
     * เก็บสิทธิ์ทั้งหมดลง session หลัง login สำเร็จ
     * @param int $role_id ID ของ role
     * @return bool true ถ้าสำเร็จ
     */
    public static function set_user_permission_to_session($role_id)
    {
        self::ensure_session_started();

        $permissions = self::get_user_permission($role_id);
        $_SESSION[self::SESSION_KEY] = $permissions;

        return !empty($permissions);
    }

    /**
     * ล้างสิทธิ์ออกจาก session
     */
    public static function clear_user_permissions()
    {
        self::ensure_session_started();
        unset($_SESSION[self::SESSION_KEY]);
    }

    /**
     * คืนค่าสิทธิ์สำหรับ JavaScript ผ่าน wp_localize_script()
     * @return array สิทธิ์ทั้งหมด
     */
    public static function get_bam_permission_for_js()
    {
        self::ensure_session_started();
        return $_SESSION[self::SESSION_KEY] ?? array();
    }

    /**
     * ตรวจสอบสิทธิ์, AJAX context และ nonce สำหรับ AJAX requests
     * @param string $permission_key คีย์สิทธิ์ที่ต้องตรวจสอบ
     * @param string $nonce_action action สำหรับ nonce (optional)
     */
    public static function verify_ajax_permission($permission_key, $nonce_action = null)
    {
        // ตรวจสอบสิทธิ์การเข้าถึง
        self::check_permission($permission_key);

        // ตรวจสอบ AJAX context
        self::verify_ajax_context();

        // ตรวจสอบ nonce
        self::verify_ajax_nonce($nonce_action);
    }

    /**
     * ตรวจสอบว่าอยู่ใน AJAX context หรือไม่
     */
    private static function verify_ajax_context()
    {
        if (!defined('DOING_AJAX') || !constant('DOING_AJAX')) {
            wp_send_json_error(array('message' => 'Invalid context'));
        }
    }

    /**
     * ตรวจสอบ nonce สำหรับ AJAX request
     * @param string|null $nonce_action action สำหรับ nonce
     */
    private static function verify_ajax_nonce($nonce_action = null)
    {
        $nonce_action = $nonce_action ?: self::NONCE_ACTION;
        $nonce_value = $_REQUEST['nonce'] ?? '';

        if (empty($nonce_value) || !wp_verify_nonce($nonce_value, $nonce_action)) {
            wp_send_json_error(array('message' => 'Invalid nonce'));
        }
    }

    /**
     * ตรวจสอบสิทธิ์หลายรายการพร้อมกัน
     * @param array $permission_keys array ของ permission keys
     * @param string $operator 'AND' หรือ 'OR' (default: 'AND')
     * @return bool true ถ้าผ่านการตรวจสอบ
     */
    public static function check_multiple_permissions($permission_keys, $operator = 'AND')
    {
        if (!is_array($permission_keys) || empty($permission_keys)) {
            return false;
        }

        $results = array();
        foreach ($permission_keys as $key) {
            $results[] = self::has_permission($key);
        }

        if ($operator === 'OR') {
            return in_array(true, $results, true);
        }

        // Default: AND operator
        return !in_array(false, $results, true);
    }

    /**
     * ดึงรายการสิทธิ์ทั้งหมดที่ผู้ใช้มี
     * @return array รายการ permission keys ที่มีค่าเป็น 1
     */
    public static function get_user_active_permissions()
    {
        self::ensure_session_started();
        $permissions = $_SESSION[self::SESSION_KEY] ?? array();

        return array_keys(array_filter($permissions, function($value) {
            return intval($value) === 1;
        }));
    }
}

// ฟังก์ชันสำหรับ backward compatibility
/**
 * ดึงสิทธิ์ตาม role_id (backward compatibility)
 * @param int $role_id ID ของ role
 * @return array สิทธิ์ทั้งหมด
 */
function get_user_permission($role_id)
{
    return BAM_Permission_Handler::get_user_permission($role_id);
}

/**
 * ตรวจสอบสิทธิ์การเข้าถึง (backward compatibility)
 * @param string $permission_key คีย์สิทธิ์
 */
function check_permission($permission_key)
{
    BAM_Permission_Handler::check_permission($permission_key);
}

/**
 * เก็บสิทธิ์ลง session (backward compatibility)
 * @param int $role_id ID ของ role
 */
function set_user_permission_to_session($role_id)
{
    BAM_Permission_Handler::set_user_permission_to_session($role_id);
}

/**
 * คืนค่าสิทธิ์สำหรับ JavaScript (backward compatibility)
 * @return array สิทธิ์ทั้งหมด
 */
function get_bam_permission_for_js()
{
    return BAM_Permission_Handler::get_bam_permission_for_js();
}

/**
 * ตรวจสอบสิทธิ์สำหรับ AJAX (backward compatibility)
 * @param string $permission_key คีย์สิทธิ์
 */
function verify_ajax_permission($permission_key)
{
    BAM_Permission_Handler::verify_ajax_permission($permission_key);
}

/**
 * ตรวจสอบว่ามีสิทธิ์หรือไม่ (helper function)
 * @param string $permission_key คีย์สิทธิ์
 * @return bool true ถ้ามีสิทธิ์
 */
function has_permission($permission_key)
{
    return BAM_Permission_Handler::has_permission($permission_key);
}
?>