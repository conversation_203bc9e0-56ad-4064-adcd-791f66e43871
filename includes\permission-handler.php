<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าถึงโดยตรง

// ดึงสิทธิ์ตาม role_id
function get_user_permission($role_id) {
    global $wpdb;
    $table = $wpdb->prefix . 'userrole';

    $permission = $wpdb->get_row(
        $wpdb->prepare("SELECT * FROM $table WHERE id = %d LIMIT 1", $role_id),
        ARRAY_A
    );

    // เอาเฉพาะฟิลด์สิทธิ์ (ตัด id, role_name, created_at ทิ้ง)
    if ($permission) {
        unset($permission['id'], $permission['role_name'], $permission['created_at']);
    }

    return $permission;
}

// ตรวจสอบสิทธิ์การเข้าถึง
function check_permission($permission_key) {
    if (!isset($_SESSION)) session_start();

    if (
        !isset($_SESSION['bam_permission'][$permission_key]) ||
        intval($_SESSION['bam_permission'][$permission_key]) !== 1
    ) {
        // 🔒 ป้องกัน headers already sent
        if (!headers_sent()) {
            wp_redirect(home_url('/dashboard'));
            exit;
        } else {
            echo '<script>window.location.href = "' . esc_url(home_url('/dashboard')) . '";</script>';
            exit;
        }
    }
}

// เก็บสิทธิ์ทั้งหมดลง session หลัง login สำเร็จ
function set_user_permission_to_session($role_id) {
    if (!isset($_SESSION)) session_start();
    $_SESSION['bam_permission'] = get_user_permission($role_id) ?? [];
}

// คืนค่าตัวแปรไปยัง JavaScript ผ่าน wp_localize_script()
function get_bam_permission_for_js() {
    if (!isset($_SESSION)) session_start();
    return $_SESSION['bam_permission'] ?? [];
}

// ตรวจสอบสิทธิ์, AJAX context และ nonce (สำหรับ wp_ajax)
function verify_ajax_permission($permission_key) {
    check_permission($permission_key);

    if (!defined('DOING_AJAX') || !DOING_AJAX) {
        wp_send_json_error(['message' => 'Invalid context']);
    }

    if (!isset($_REQUEST['nonce']) || !wp_verify_nonce($_REQUEST['nonce'], 'bam_nonce_action')) {
        wp_send_json_error(['message' => 'Invalid nonce']);
    }
}
?>