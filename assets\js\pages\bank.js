jQuery(function ($) {
    console.log("Display-bank.js is ready.");

    window.initializeBankTables = function () {
        const $table = $('#display_bank');
        if (!$table.length) return;

        const bankType = $table.data('bank-type');

        $.ajax({
            url: bamAjaxConfig.ajaxurl,
            method: 'GET',
            data: {
                action: 'load_bank_data',
                type: bankType,
                nonce: bamAjaxConfig.nonce
            },
            success: function (response) {
                if (response.success && Array.isArray(response.data)) {
                    const rows = response.data.map((item, index) => `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${item.bank_name}</td>
                            <td>${item.account_name}</td>
                            <td>${item.bank_number}</td>
                            <td>${item.atm_pin || ''}</td>
                            <td>${item.app_pin || ''}</td>
                            <td>${item.agent_name || ''}</td>
                            <td>${item.activated_date || ''}</td>
                            <td>${item.notes || ''}</td>
                            <td class="text-center">${item.status}</td>
                            <td class="text-center">
                                <div class="dropdown">
                                    <button class="btn btn-primary" type="button" data-bs-toggle="dropdown">ดำเนินการ <i class="bi bi-chevron-down"></i></button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item text-white" href="#" data-action="view" data-id="${item.id}"><i class="bi bi-info-circle-fill"></i> ดูข้อมูล</a></li>
                                        <li><a class="dropdown-item text-white" href="#" data-action="edit" data-id="${item.id}"><i class="bi bi-gear-fill"></i> แก้ไข</a></li>
                                        <li><a class="dropdown-item text-warning" href="#" data-action="deactivate" data-id="${item.id}"><i class="bi bi-x-circle-fill"></i> ยกเลิกใช้งาน</a></li>
                                        <li><a class="dropdown-item text-danger" href="#" data-action="delete" data-id="${item.id}"><i class="bi bi-trash-fill"></i> ลบ</a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    `).join('');

                    $table.find('tbody').html(rows);

                    if (typeof initializeDataTable === 'function') {
                        initializeDataTable($table);
                    }
                } else {
                    console.warn("โหลดข้อมูลไม่สำเร็จ");
                }
            },
            error: function () {
                console.error("เกิดข้อผิดพลาดในการโหลดข้อมูลจาก AJAX");
            }
        });
    };

    if ($('#display_bank').length > 0 && typeof window.initializeBankTables === 'function') {
        window.initializeBankTables(); // ✅ เรียกเฉพาะของ bank
    }
});