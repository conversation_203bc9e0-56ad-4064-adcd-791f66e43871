<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

// 🔒 ควรเก็บค่าเหล่านี้ใน wp-config.php หรือ .env และโหลดผ่าน getenv()
// หรือใช้ define แบบด้านล่างนี้ (หากไม่มี .env)
if (!defined('BAM_ENCRYPT_KEY')) {
    define('BAM_ENCRYPT_KEY', 'ใส่คีย์ความยาว32ตัวอักษรเช่น****************7890123456789012');
}

if (!defined('BAM_ENCRYPT_IV')) {
    define('BAM_ENCRYPT_IV', 'ใส่IVยาว16ตัวอักษรเช่น****************');
}

/**
 * ฟังก์ชันเข้ารหัสข้อมูลสำคัญ เช่น account_name, bank_number, etc.
 *
 * @param string $data ข้อมูลเดิมที่ยังไม่เข้ารหัส
 * @return string ข้อมูลที่เข้ารหัสแบบ base64 แล้ว
 */
function bam_encrypt($data) {
    if (!$data) return null;

    $encrypted = openssl_encrypt($data, 'AES-256-CBC', BAM_ENCRYPT_KEY, OPENSSL_RAW_DATA, BAM_ENCRYPT_IV);
    return base64_encode($encrypted);
}

/**
 * ฟังก์ชันถอดรหัสข้อมูลเพื่อนำไปแสดง
 *
 * @param string $encrypted base64 string ที่ถูกเข้ารหัสไว้
 * @return string|null ข้อมูลเดิมที่ถอดรหัสแล้ว
 */
function bam_decrypt($encrypted) {
    if (!$encrypted) return null;

    $decoded = base64_decode($encrypted);
    return openssl_decrypt($decoded, 'AES-256-CBC', BAM_ENCRYPT_KEY, OPENSSL_RAW_DATA, BAM_ENCRYPT_IV);
}