<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

/**
 * BAM Encryption Manager
 * คลาสสำหรับจัดการการเข้ารหัสและถอดรหัสข้อมูลสำคัญในระบบ BAM
 * ใช้ AES-256-CBC encryption algorithm เพื่อความปลอดภัยสูงสุด
 */
class BAM_Encryption_Manager
{
    // ค่าคงที่สำหรับการเข้ารหัส
    private const CIPHER_METHOD = 'AES-256-CBC';
    private const KEY_LENGTH = 32; // 256 bits
    private const IV_LENGTH = 16;  // 128 bits

    // ข้อความ error มาตรฐาน
    private const ERROR_INVALID_KEY = 'Encryption key is invalid or not set';
    private const ERROR_INVALID_IV = 'Encryption IV is invalid or not set';
    private const ERROR_ENCRYPTION_FAILED = 'Data encryption failed';
    private const ERROR_DECRYPTION_FAILED = 'Data decryption failed';
    private const ERROR_INVALID_INPUT = 'Invalid input data provided';

    /**
     * เริ่มต้นการตั้งค่า encryption keys
     */
    public static function init()
    {
        self::initialize_encryption_constants();
        self::validate_encryption_setup();
    }

    /**
     * ตั้งค่า constants สำหรับ encryption
     */
    private static function initialize_encryption_constants()
    {
        // ลองโหลดจาก environment variables ก่อน (แนะนำสำหรับ production)
        $key_from_env = getenv('BAM_ENCRYPT_KEY');
        $iv_from_env = getenv('BAM_ENCRYPT_IV');

        // ถ้าไม่มีใน environment ให้ใช้ค่า default (สำหรับ development)
        if (!defined('BAM_ENCRYPT_KEY')) {
            $default_key = $key_from_env ?: 'ใส่คีย์ความยาว32ตัวอักษรเช่น12345678901234567890123456789012';
            define('BAM_ENCRYPT_KEY', $default_key);
        }

        if (!defined('BAM_ENCRYPT_IV')) {
            $default_iv = $iv_from_env ?: 'ใส่คีย์ความยาว16ตัวอักษรเช่น1234567890123456';
            define('BAM_ENCRYPT_IV', $default_iv);
        }
    }

    /**
     * ตรวจสอบความถูกต้องของการตั้งค่า encryption
     * @throws Exception หากการตั้งค่าไม่ถูกต้อง
     */
    private static function validate_encryption_setup()
    {
        if (!self::is_valid_key(BAM_ENCRYPT_KEY)) {
            throw new Exception(self::ERROR_INVALID_KEY);
        }

        if (!self::is_valid_iv(BAM_ENCRYPT_IV)) {
            throw new Exception(self::ERROR_INVALID_IV);
        }

        if (!extension_loaded('openssl')) {
            throw new Exception('OpenSSL extension is required for encryption');
        }
    }

    /**
     * ตรวจสอบความถูกต้องของ encryption key
     * @param string $key คีย์ที่ต้องตรวจสอบ
     * @return bool true ถ้าคีย์ถูกต้อง
     */
    private static function is_valid_key($key)
    {
        return is_string($key) && strlen($key) === self::KEY_LENGTH;
    }

    /**
     * ตรวจสอบความถูกต้องของ IV (Initialization Vector)
     * @param string $iv IV ที่ต้องตรวจสอบ
     * @return bool true ถ้า IV ถูกต้อง
     */
    private static function is_valid_iv($iv)
    {
        return is_string($iv) && strlen($iv) === self::IV_LENGTH;
    }

    /**
     * เข้ารหัสข้อมูลสำคัญ
     * @param mixed $data ข้อมูลที่ต้องการเข้ารหัส
     * @return string|null ข้อมูลที่เข้ารหัสแล้วในรูปแบบ base64 หรือ null หากไม่สำเร็จ
     */
    public static function encrypt($data)
    {
        try {
            // ตรวจสอบข้อมูลนำเข้า
            if (!self::is_valid_input_data($data)) {
                return null;
            }

            // แปลงข้อมูลเป็น string
            $string_data = self::prepare_data_for_encryption($data);

            // ทำการเข้ารหัส
            $encrypted = openssl_encrypt(
                $string_data,
                self::CIPHER_METHOD,
                BAM_ENCRYPT_KEY,
                OPENSSL_RAW_DATA,
                BAM_ENCRYPT_IV
            );

            // ตรวจสอบผลลัพธ์
            if ($encrypted === false) {
                self::log_encryption_error('Encryption failed', $data);
                return null;
            }

            return base64_encode($encrypted);

        } catch (Exception $e) {
            self::log_encryption_error('Encryption exception: ' . $e->getMessage(), $data);
            return null;
        }
    }

    /**
     * ถอดรหัสข้อมูล
     * @param string $encrypted_data ข้อมูลที่เข้ารหัสแล้วในรูปแบบ base64
     * @return string|null ข้อมูลที่ถอดรหัสแล้ว หรือ null หากไม่สำเร็จ
     */
    public static function decrypt($encrypted_data)
    {
        try {
            // ตรวจสอบข้อมูลนำเข้า
            if (!self::is_valid_encrypted_data($encrypted_data)) {
                return null;
            }

            // ถอดรหัส base64
            $decoded = base64_decode($encrypted_data, true);
            if ($decoded === false) {
                self::log_decryption_error('Base64 decode failed', $encrypted_data);
                return null;
            }

            // ถอดรหัสข้อมูล
            $decrypted = openssl_decrypt(
                $decoded,
                self::CIPHER_METHOD,
                BAM_ENCRYPT_KEY,
                OPENSSL_RAW_DATA,
                BAM_ENCRYPT_IV
            );

            // ตรวจสอบผลลัพธ์
            if ($decrypted === false) {
                self::log_decryption_error('Decryption failed', $encrypted_data);
                return null;
            }

            return $decrypted;

        } catch (Exception $e) {
            self::log_decryption_error('Decryption exception: ' . $e->getMessage(), $encrypted_data);
            return null;
        }
    }

    /**
     * เข้ารหัสข้อมูลหลายฟิลด์พร้อมกัน
     * @param array $data_array array ของข้อมูลที่ต้องการเข้ารหัส
     * @return array array ของข้อมูลที่เข้ารหัสแล้ว
     */
    public static function encrypt_multiple($data_array)
    {
        if (!is_array($data_array)) {
            return array();
        }

        $encrypted_array = array();
        foreach ($data_array as $key => $value) {
            $encrypted_array[$key] = self::encrypt($value);
        }

        return $encrypted_array;
    }

    /**
     * ถอดรหัสข้อมูลหลายฟิลด์พร้อมกัน
     * @param array $encrypted_array array ของข้อมูลที่เข้ารหัสแล้ว
     * @return array array ของข้อมูลที่ถอดรหัสแล้ว
     */
    public static function decrypt_multiple($encrypted_array)
    {
        if (!is_array($encrypted_array)) {
            return array();
        }

        $decrypted_array = array();
        foreach ($encrypted_array as $key => $value) {
            $decrypted_array[$key] = self::decrypt($value);
        }

        return $decrypted_array;
    }

    /**
     * ตรวจสอบความถูกต้องของข้อมูลนำเข้า
     * @param mixed $data ข้อมูลที่ต้องตรวจสอบ
     * @return bool true ถ้าข้อมูลถูกต้อง
     */
    private static function is_valid_input_data($data)
    {
        return $data !== null && $data !== '' && $data !== false;
    }

    /**
     * ตรวจสอบความถูกต้องของข้อมูลที่เข้ารหัสแล้ว
     * @param string $encrypted_data ข้อมูลที่เข้ารหัสแล้ว
     * @return bool true ถ้าข้อมูลถูกต้อง
     */
    private static function is_valid_encrypted_data($encrypted_data)
    {
        return is_string($encrypted_data) && !empty($encrypted_data);
    }

    /**
     * เตรียมข้อมูลสำหรับการเข้ารหัส
     * @param mixed $data ข้อมูลที่ต้องเตรียม
     * @return string ข้อมูลในรูปแบบ string
     */
    private static function prepare_data_for_encryption($data)
    {
        if (is_string($data)) {
            return $data;
        }

        if (is_numeric($data)) {
            return (string) $data;
        }

        if (is_array($data) || is_object($data)) {
            return json_encode($data);
        }

        return (string) $data;
    }

    /**
     * บันทึก error ของการเข้ารหัส
     * @param string $message ข้อความ error
     * @param mixed $data ข้อมูลที่เกิดปัญหา
     */
    private static function log_encryption_error($message, $data)
    {
        if (defined('WP_DEBUG') && constant('WP_DEBUG')) {
            error_log("BAM Encryption Error: {$message}. Data type: " . gettype($data));
        }
    }

    /**
     * บันทึก error ของการถอดรหัส
     * @param string $message ข้อความ error
     * @param string $encrypted_data ข้อมูลที่เกิดปัญหา
     */
    private static function log_decryption_error($message, $encrypted_data)
    {
        if (defined('WP_DEBUG') && constant('WP_DEBUG')) {
            $data_preview = substr($encrypted_data, 0, 20) . '...';
            error_log("BAM Decryption Error: {$message}. Data preview: {$data_preview}");
        }
    }
}

// เริ่มต้นการตั้งค่า encryption เมื่อโหลดไฟล์
try {
    BAM_Encryption_Manager::init();
} catch (Exception $e) {
    if (defined('WP_DEBUG') && constant('WP_DEBUG')) {
        error_log('BAM Encryption initialization failed: ' . $e->getMessage());
    }
}

/**
 * ฟังก์ชันเข้ารหัสข้อมูลสำคัญ (backward compatibility)
 * @param mixed $data ข้อมูลที่ต้องการเข้ารหัส
 * @return string|null ข้อมูลที่เข้ารหัสแล้ว
 */
function bam_encrypt($data)
{
    return BAM_Encryption_Manager::encrypt($data);
}

/**
 * ฟังก์ชันถอดรหัสข้อมูล (backward compatibility)
 * @param string $encrypted_data ข้อมูลที่เข้ารหัสแล้ว
 * @return string|null ข้อมูลที่ถอดรหัสแล้ว
 */
function bam_decrypt($encrypted_data)
{
    return BAM_Encryption_Manager::decrypt($encrypted_data);
}

/**
 * ฟังก์ชันเข้ารหัสข้อมูลหลายฟิลด์พร้อมกัน
 * @param array $data_array array ของข้อมูลที่ต้องการเข้ารหัส
 * @return array array ของข้อมูลที่เข้ารหัสแล้ว
 */
function bam_encrypt_multiple($data_array)
{
    return BAM_Encryption_Manager::encrypt_multiple($data_array);
}

/**
 * ฟังก์ชันถอดรหัสข้อมูลหลายฟิลด์พร้อมกัน
 * @param array $encrypted_array array ของข้อมูลที่เข้ารหัสแล้ว
 * @return array array ของข้อมูลที่ถอดรหัสแล้ว
 */
function bam_decrypt_multiple($encrypted_array)
{
    return BAM_Encryption_Manager::decrypt_multiple($encrypted_array);
}