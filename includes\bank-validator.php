<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

// ✅ ตรวจสอบเลขบัญชี (ต้องเป็นตัวเลข 10-20 หลัก)
function is_valid_bank_number($bank_number) {
    return preg_match('/^\d{1,20}$/', $bank_number);
}

// ✅ ตรวจสอบว่า PIN เป็นตัวเลข 4-6 หลัก
function is_valid_pin($pin) {
    return preg_match('/^\d{4,6}$/', $pin);
}

// ✅ ตรวจสอบว่าเลขบัตรประชาชนเป็นตัวเลข 13 หลัก
function is_valid_national_id($nid) {
    return preg_match('/^\d{13}$/', $nid);
}

// ✅ ตรวจสอบเบอร์โทรศัพท์ไทย 10 หลัก
function is_valid_phone_number($phone) {
    return preg_match('/^\d{10}$/', $phone);
}

// ✅ ตรวจสอบว่า status ถูกต้องตามระบบกำหนด
function is_valid_status($status) {
    return in_array($status, ['ใช้งาน', 'ยกเลิก', 'เตรียมพร้อม']);
}

// ✅ ตรวจสอบว่า MIME Type เป็นภาพ JPG หรือ PNG
function is_valid_image_type($mime) {
    return in_array($mime, ['image/jpeg', 'image/png']);
}

// ✅ ตรวจสอบชนิดไฟล์จาก exif image type
function is_real_image($tmp_path) {
    $allowed_types = [IMAGETYPE_JPEG, IMAGETYPE_PNG];
    $type = exif_imagetype($tmp_path);
    return in_array($type, $allowed_types);
}
?>