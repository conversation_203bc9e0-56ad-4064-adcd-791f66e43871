<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

/**
 * BAM Bank Validator Class
 * คลาสสำหรับตรวจสอบความถูกต้องของข้อมูลธนาคารและข้อมูลส่วนบุคคลในระบบ BAM
 */
class BAM_Bank_Validator
{
    // ค่าคงที่สำหรับการตรวจสอบ
    private const BANK_NUMBER_MIN_LENGTH = 10;
    private const BANK_NUMBER_MAX_LENGTH = 20;
    private const PIN_MIN_LENGTH = 4;
    private const PIN_MAX_LENGTH = 6;
    private const NATIONAL_ID_LENGTH = 13;
    private const PHONE_NUMBER_LENGTH = 10;

    // รายการสถานะที่อนุญาต
    private const ALLOWED_STATUSES = [
        'ใช้งาน',
        'ยกเลิก',
        'เตรียมพร้อม'
    ];

    // รายการธนาคารที่รองรับ
    private const SUPPORTED_BANKS = [
        'ธนาคารไทยพาณิชย์',
        'ธนาคารกสิกรไทย',
        'ธนาคารกรุงไทย',
        'ธนาคารกรุงเทพ',
        'ธนาคารทหารไทยธนชาต',
        'ธนาคารกรุงศรีอยุธยา',
        'ธนาคารออมสิน',
        'ธนาคารอาคารสงเคราะห์',
        'ธนาคารเกียรตินาคินภัทร',
        'ธนาคารซีไอเอ็มบี ไทย',
        'ธนาคารยูโอบี',
        'ธนาคารแลนด์ แอนด์ เฮ้าส์'
    ];

    // รายการประเภทธนาคารที่รองรับ
    private const SUPPORTED_BANK_TYPES = [
        'f789',
        's888',
        'jnp',
        'kaw',
        'czo'
    ];

    // MIME types ที่อนุญาตสำหรับรูปภาพ
    private const ALLOWED_IMAGE_MIME_TYPES = [
        'image/jpeg',
        'image/png',
        'image/webp'
    ];

    // Image types ที่อนุญาต (สำหรับ exif_imagetype)
    private const ALLOWED_IMAGE_TYPES = [
        IMAGETYPE_JPEG,
        IMAGETYPE_PNG,
        IMAGETYPE_WEBP
    ];

    // รูปแบบ regex สำหรับการตรวจสอบ
    private const REGEX_PATTERNS = [
        'digits_only' => '/^\d+$/',
        'thai_name' => '/^[ก-๙\s\.]+$/u',
        'english_name' => '/^[a-zA-Z\s\.]+$/',
        'mixed_name' => '/^[ก-๙a-zA-Z\s\.]+$/u',
        'date' => '/^\d{4}-\d{2}-\d{2}$/',
        'email' => '/^[^\s@]+@[^\s@]+\.[^\s@]+$/'
    ];

    /**
     * ตรวจสอบเลขบัญชีธนาคาร
     * @param string $bank_number เลขบัญชีธนาคาร
     * @param array $options ตัวเลือกเพิ่มเติม
     * @return bool true ถ้าถูกต้อง
     */
    public static function is_valid_bank_number(string $bank_number, array $options = []): bool
    {
        // ทำความสะอาดข้อมูล
        $bank_number = self::sanitize_numeric_input($bank_number);

        if (empty($bank_number)) {
            return false;
        }

        // ตรวจสอบความยาว
        $min_length = $options['min_length'] ?? self::BANK_NUMBER_MIN_LENGTH;
        $max_length = $options['max_length'] ?? self::BANK_NUMBER_MAX_LENGTH;

        $length = strlen($bank_number);
        if ($length < $min_length || $length > $max_length) {
            return false;
        }

        // ตรวจสอบว่าเป็นตัวเลขเท่านั้น
        if (!self::is_digits_only($bank_number)) {
            return false;
        }

        // ตรวจสอบรูปแบบเพิ่มเติม (ถ้ามี)
        if (isset($options['custom_pattern'])) {
            return (bool) preg_match($options['custom_pattern'], $bank_number);
        }

        return true;
    }

    /**
     * ตรวจสอบ PIN (ATM หรือ App)
     * @param string $pin รหัส PIN
     * @param array $options ตัวเลือกเพิ่มเติม
     * @return bool true ถ้าถูกต้อง
     */
    public static function is_valid_pin(string $pin, array $options = []): bool
    {
        // ทำความสะอาดข้อมูล
        $pin = self::sanitize_numeric_input($pin);

        if (empty($pin)) {
            return !($options['required'] ?? false);
        }

        // ตรวจสอบความยาว
        $min_length = $options['min_length'] ?? self::PIN_MIN_LENGTH;
        $max_length = $options['max_length'] ?? self::PIN_MAX_LENGTH;

        $length = strlen($pin);
        if ($length < $min_length || $length > $max_length) {
            return false;
        }

        // ตรวจสอบว่าเป็นตัวเลขเท่านั้น
        if (!self::is_digits_only($pin)) {
            return false;
        }

        // ตรวจสอบรูปแบบที่ไม่ปลอดภัย (เลขซ้ำ, ลำดับ)
        if ($options['check_security'] ?? true) {
            if (self::is_weak_pin($pin)) {
                return false;
            }
        }

        return true;
    }

    /**
     * ตรวจสอบเลขบัตรประชาชน
     * @param string $national_id เลขบัตรประชาชน
     * @param array $options ตัวเลือกเพิ่มเติม
     * @return bool true ถ้าถูกต้อง
     */
    public static function is_valid_national_id(string $national_id, array $options = []): bool
    {
        // ทำความสะอาดข้อมูล
        $national_id = self::sanitize_numeric_input($national_id);

        if (empty($national_id)) {
            return !($options['required'] ?? false);
        }

        // ตรวจสอบความยาว
        if (strlen($national_id) !== self::NATIONAL_ID_LENGTH) {
            return false;
        }

        // ตรวจสอบว่าเป็นตัวเลขเท่านั้น
        if (!self::is_digits_only($national_id)) {
            return false;
        }

        // ตรวจสอบ checksum (ถ้าต้องการ)
        if ($options['validate_checksum'] ?? true) {
            return self::validate_national_id_checksum($national_id);
        }

        return true;
    }

    /**
     * ตรวจสอบเบอร์โทรศัพท์ไทย
     * @param string $phone_number เบอร์โทรศัพท์
     * @param array $options ตัวเลือกเพิ่มเติม
     * @return bool true ถ้าถูกต้อง
     */
    public static function is_valid_phone_number(string $phone_number, array $options = []): bool
    {
        // ทำความสะอาดข้อมูล
        $phone_number = self::sanitize_phone_number($phone_number);

        if (empty($phone_number)) {
            return !($options['required'] ?? false);
        }

        // ตรวจสอบความยาว
        if (strlen($phone_number) !== self::PHONE_NUMBER_LENGTH) {
            return false;
        }

        // ตรวจสอบว่าเป็นตัวเลขเท่านั้น
        if (!self::is_digits_only($phone_number)) {
            return false;
        }

        // ตรวจสอบรูปแบบเบอร์โทรไทย
        if ($options['validate_thai_format'] ?? true) {
            return self::is_valid_thai_phone_format($phone_number);
        }

        return true;
    }

    /**
     * ตรวจสอบสถานะ
     * @param string $status สถานะ
     * @param array $custom_statuses สถานะเพิ่มเติม (optional)
     * @return bool true ถ้าถูกต้อง
     */
    public static function is_valid_status(string $status, array $custom_statuses = []): bool
    {
        $status = trim($status);

        if (empty($status)) {
            return false;
        }

        $allowed_statuses = empty($custom_statuses) ? self::ALLOWED_STATUSES : $custom_statuses;

        return in_array($status, $allowed_statuses, true);
    }

    /**
     * ตรวจสอบชื่อธนาคาร
     * @param string $bank_name ชื่อธนาคาร
     * @return bool true ถ้าถูกต้อง
     */
    public static function is_valid_bank_name(string $bank_name): bool
    {
        $bank_name = trim($bank_name);

        if (empty($bank_name)) {
            return false;
        }

        return in_array($bank_name, self::SUPPORTED_BANKS, true);
    }

    /**
     * ตรวจสอบประเภทธนาคาร
     * @param string $bank_type ประเภทธนาคาร
     * @return bool true ถ้าถูกต้อง
     */
    public static function is_valid_bank_type(string $bank_type): bool
    {
        $bank_type = trim($bank_type);

        if (empty($bank_type)) {
            return false;
        }

        return in_array($bank_type, self::SUPPORTED_BANK_TYPES, true);
    }

    /**
     * ตรวจสอบชื่อบัญชี
     * @param string $account_name ชื่อบัญชี
     * @param array $options ตัวเลือกเพิ่มเติม
     * @return bool true ถ้าถูกต้อง
     */
    public static function is_valid_account_name(string $account_name, array $options = []): bool
    {
        $account_name = trim($account_name);

        if (empty($account_name)) {
            return false;
        }

        // ตรวจสอบความยาว
        $min_length = $options['min_length'] ?? 2;
        $max_length = $options['max_length'] ?? 100;

        $length = mb_strlen($account_name, 'UTF-8');
        if ($length < $min_length || $length > $max_length) {
            return false;
        }

        // ตรวจสอบรูปแบบตัวอักษร
        $allow_thai = $options['allow_thai'] ?? true;
        $allow_english = $options['allow_english'] ?? true;

        if ($allow_thai && $allow_english) {
            return (bool) preg_match(self::REGEX_PATTERNS['mixed_name'], $account_name);
        } elseif ($allow_thai) {
            return (bool) preg_match(self::REGEX_PATTERNS['thai_name'], $account_name);
        } elseif ($allow_english) {
            return (bool) preg_match(self::REGEX_PATTERNS['english_name'], $account_name);
        }

        return false;
    }

    /**
     * ตรวจสอบวันที่
     * @param string $date วันที่ในรูปแบบ YYYY-MM-DD
     * @param array $options ตัวเลือกเพิ่มเติม
     * @return bool true ถ้าถูกต้อง
     */
    public static function is_valid_date(string $date, array $options = []): bool
    {
        $date = trim($date);

        if (empty($date)) {
            return !($options['required'] ?? false);
        }

        // ตรวจสอบรูปแบบ
        if (!preg_match(self::REGEX_PATTERNS['date'], $date)) {
            return false;
        }

        // ตรวจสอบความถูกต้องของวันที่
        $date_parts = explode('-', $date);
        if (count($date_parts) !== 3) {
            return false;
        }

        [$year, $month, $day] = array_map('intval', $date_parts);

        if (!checkdate($month, $day, $year)) {
            return false;
        }

        // ตรวจสอบช่วงวันที่ (ถ้ามี)
        if (isset($options['min_date']) || isset($options['max_date'])) {
            $timestamp = strtotime($date);

            if (isset($options['min_date'])) {
                $min_timestamp = strtotime($options['min_date']);
                if ($timestamp < $min_timestamp) {
                    return false;
                }
            }

            if (isset($options['max_date'])) {
                $max_timestamp = strtotime($options['max_date']);
                if ($timestamp > $max_timestamp) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * ตรวจสอบ MIME type ของรูปภาพ
     * @param string $mime_type MIME type
     * @return bool true ถ้าถูกต้อง
     */
    public static function is_valid_image_type(string $mime_type): bool
    {
        return in_array($mime_type, self::ALLOWED_IMAGE_MIME_TYPES, true);
    }

    /**
     * ตรวจสอบว่าเป็นรูปภาพจริงหรือไม่
     * @param string $file_path path ของไฟล์
     * @return bool true ถ้าเป็นรูปภาพจริง
     */
    public static function is_real_image(string $file_path): bool
    {
        if (!file_exists($file_path) || !is_readable($file_path)) {
            return false;
        }

        $image_type = @exif_imagetype($file_path);

        return $image_type && in_array($image_type, self::ALLOWED_IMAGE_TYPES, true);
    }

    /**
     * ตรวจสอบอีเมล
     * @param string $email อีเมล
     * @param array $options ตัวเลือกเพิ่มเติม
     * @return bool true ถ้าถูกต้อง
     */
    public static function is_valid_email(string $email, array $options = []): bool
    {
        $email = trim($email);

        if (empty($email)) {
            return !($options['required'] ?? false);
        }

        // ตรวจสอบรูปแบบพื้นฐาน
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return false;
        }

        // ตรวจสอบรูปแบบเพิ่มเติม
        if (!preg_match(self::REGEX_PATTERNS['email'], $email)) {
            return false;
        }

        // ตรวจสอบ domain ที่อนุญาต (ถ้ามี)
        if (isset($options['allowed_domains'])) {
            $domain = substr(strrchr($email, '@'), 1);
            return in_array($domain, $options['allowed_domains'], true);
        }

        return true;
    }

    /**
     * ตรวจสอบข้อมูลธนาคารทั้งหมด
     * @param array $data ข้อมูลธนาคาร
     * @param array $options ตัวเลือกเพิ่มเติม
     * @return array ผลการตรวจสอบ
     */
    public static function validate_bank_data(array $data, array $options = []): array
    {
        $errors = [];
        $validated_data = [];

        // ตรวจสอบประเภทธนาคาร
        if (!self::is_valid_bank_type($data['bank_type'] ?? '')) {
            $errors['bank_type'] = 'ประเภทธนาคารไม่ถูกต้อง';
        } else {
            $validated_data['bank_type'] = trim($data['bank_type']);
        }

        // ตรวจสอบชื่อธนาคาร
        if (!self::is_valid_bank_name($data['bank_name'] ?? '')) {
            $errors['bank_name'] = 'ชื่อธนาคารไม่ถูกต้อง';
        } else {
            $validated_data['bank_name'] = trim($data['bank_name']);
        }

        // ตรวจสอบชื่อบัญชี
        if (!self::is_valid_account_name($data['account_name'] ?? '', $options['account_name'] ?? [])) {
            $errors['account_name'] = 'ชื่อบัญชีไม่ถูกต้อง';
        } else {
            $validated_data['account_name'] = trim($data['account_name']);
        }

        // ตรวจสอบเลขบัญชี
        if (!self::is_valid_bank_number($data['bank_number'] ?? '', $options['bank_number'] ?? [])) {
            $errors['bank_number'] = 'เลขบัญชีไม่ถูกต้อง';
        } else {
            $validated_data['bank_number'] = self::sanitize_numeric_input($data['bank_number']);
        }

        // ตรวจสอบ ATM PIN (ถ้ามี)
        if (!empty($data['atm_pin'])) {
            if (!self::is_valid_pin($data['atm_pin'], $options['atm_pin'] ?? [])) {
                $errors['atm_pin'] = 'ATM PIN ไม่ถูกต้อง';
            } else {
                $validated_data['atm_pin'] = self::sanitize_numeric_input($data['atm_pin']);
            }
        }

        // ตรวจสอบ App PIN (ถ้ามี)
        if (!empty($data['app_pin'])) {
            if (!self::is_valid_pin($data['app_pin'], $options['app_pin'] ?? [])) {
                $errors['app_pin'] = 'App PIN ไม่ถูกต้อง';
            } else {
                $validated_data['app_pin'] = self::sanitize_numeric_input($data['app_pin']);
            }
        }

        // ตรวจสอบเบอร์โทรศัพท์ (ถ้ามี)
        if (!empty($data['phone_number'])) {
            if (!self::is_valid_phone_number($data['phone_number'], $options['phone_number'] ?? [])) {
                $errors['phone_number'] = 'เบอร์โทรศัพท์ไม่ถูกต้อง';
            } else {
                $validated_data['phone_number'] = self::sanitize_phone_number($data['phone_number']);
            }
        }

        // ตรวจสอบเลขบัตรประชาชน (ถ้ามี)
        if (!empty($data['national_id'])) {
            if (!self::is_valid_national_id($data['national_id'], $options['national_id'] ?? [])) {
                $errors['national_id'] = 'เลขบัตรประชาชนไม่ถูกต้อง';
            } else {
                $validated_data['national_id'] = self::sanitize_numeric_input($data['national_id']);
            }
        }

        // ตรวจสอบวันเกิด (ถ้ามี)
        if (!empty($data['birth_date'])) {
            if (!self::is_valid_date($data['birth_date'], $options['birth_date'] ?? [])) {
                $errors['birth_date'] = 'วันเกิดไม่ถูกต้อง';
            } else {
                $validated_data['birth_date'] = trim($data['birth_date']);
            }
        }

        // ตรวจสอบวันที่เปิดใช้งาน (ถ้ามี)
        if (!empty($data['activated_date'])) {
            if (!self::is_valid_date($data['activated_date'], $options['activated_date'] ?? [])) {
                $errors['activated_date'] = 'วันที่เปิดใช้งานไม่ถูกต้อง';
            } else {
                $validated_data['activated_date'] = trim($data['activated_date']);
            }
        }

        // ตรวจสอบสถานะ
        if (!self::is_valid_status($data['status'] ?? '')) {
            $errors['status'] = 'สถานะไม่ถูกต้อง';
        } else {
            $validated_data['status'] = trim($data['status']);
        }

        return [
            'is_valid' => empty($errors),
            'errors' => $errors,
            'validated_data' => $validated_data
        ];
    }

    // ===== Private Helper Methods =====

    /**
     * ทำความสะอาดข้อมูลตัวเลข
     * @param string $input ข้อมูลที่ต้องทำความสะอาด
     * @return string ข้อมูลที่ทำความสะอาดแล้ว
     */
    private static function sanitize_numeric_input(string $input): string
    {
        return preg_replace('/\D/', '', $input);
    }

    /**
     * ทำความสะอาดเบอร์โทรศัพท์
     * @param string $phone_number เบอร์โทรศัพท์
     * @return string เบอร์โทรศัพท์ที่ทำความสะอาดแล้ว
     */
    private static function sanitize_phone_number(string $phone_number): string
    {
        // ลบอักขระที่ไม่ใช่ตัวเลข
        $phone_number = preg_replace('/\D/', '', $phone_number);

        // ลบ 0 หน้าถ้ามี และเป็นเบอร์ 11 หลัก
        if (strlen($phone_number) === 11 && substr($phone_number, 0, 1) === '0') {
            $phone_number = substr($phone_number, 1);
        }

        return $phone_number;
    }

    /**
     * ตรวจสอบว่าเป็นตัวเลขเท่านั้น
     * @param string $input ข้อมูลที่ต้องตรวจสอบ
     * @return bool true ถ้าเป็นตัวเลขเท่านั้น
     */
    private static function is_digits_only(string $input): bool
    {
        return (bool) preg_match(self::REGEX_PATTERNS['digits_only'], $input);
    }

    /**
     * ตรวจสอบว่า PIN อ่อนแอหรือไม่
     * @param string $pin รหัส PIN
     * @return bool true ถ้า PIN อ่อนแอ
     */
    private static function is_weak_pin(string $pin): bool
    {
        // ตรวจสอบเลขซ้ำ (เช่น 1111, 2222)
        if (preg_match('/^(\d)\1+$/', $pin)) {
            return true;
        }

        // ตรวจสอบลำดับเลข (เช่น 1234, 4321)
        $ascending = '';
        $descending = '';
        for ($i = 0; $i < strlen($pin); $i++) {
            $ascending .= ($i % 10);
            $descending .= ((9 - $i) % 10);
        }

        if ($pin === substr($ascending, 0, strlen($pin)) ||
            $pin === substr($descending, 0, strlen($pin))) {
            return true;
        }

        // ตรวจสอบ PIN ที่ใช้บ่อย
        $common_pins = ['1234', '0000', '1111', '1212', '7777', '1004'];
        if (in_array($pin, $common_pins, true)) {
            return true;
        }

        return false;
    }

    /**
     * ตรวจสอบ checksum ของเลขบัตรประชาชน
     * @param string $national_id เลขบัตรประชาชน
     * @return bool true ถ้า checksum ถูกต้อง
     */
    private static function validate_national_id_checksum(string $national_id): bool
    {
        if (strlen($national_id) !== 13) {
            return false;
        }

        $sum = 0;
        for ($i = 0; $i < 12; $i++) {
            $sum += (int)$national_id[$i] * (13 - $i);
        }

        $checksum = (11 - ($sum % 11)) % 10;

        return $checksum === (int)$national_id[12];
    }

    /**
     * ตรวจสอบรูปแบบเบอร์โทรไทย
     * @param string $phone_number เบอร์โทรศัพท์
     * @return bool true ถ้าเป็นรูปแบบเบอร์โทรไทย
     */
    private static function is_valid_thai_phone_format(string $phone_number): bool
    {
        // เบอร์มือถือ: 06, 08, 09
        // เบอร์บ้าน: 02, 03, 04, 05, 07
        $mobile_prefixes = ['06', '08', '09'];
        $landline_prefixes = ['02', '03', '04', '05', '07'];

        $prefix = substr($phone_number, 0, 2);

        return in_array($prefix, array_merge($mobile_prefixes, $landline_prefixes), true);
    }

    /**
     * ดึงรายการธนาคารที่รองรับ
     * @return array รายการธนาคาร
     */
    public static function get_supported_banks(): array
    {
        return self::SUPPORTED_BANKS;
    }

    /**
     * ดึงรายการประเภทธนาคารที่รองรับ
     * @return array รายการประเภทธนาคาร
     */
    public static function get_supported_bank_types(): array
    {
        return self::SUPPORTED_BANK_TYPES;
    }

    /**
     * ดึงรายการสถานะที่อนุญาต
     * @return array รายการสถานะ
     */
    public static function get_allowed_statuses(): array
    {
        return self::ALLOWED_STATUSES;
    }
}

// ฟังก์ชันสำหรับ backward compatibility
/**
 * ตรวจสอบเลขบัญชีธนาคาร (backward compatibility)
 * @param string $bank_number เลขบัญชีธนาคาร
 * @return bool true ถ้าถูกต้อง
 */
function is_valid_bank_number(string $bank_number): bool
{
    return BAM_Bank_Validator::is_valid_bank_number($bank_number);
}

/**
 * ตรวจสอบ PIN (backward compatibility)
 * @param string $pin รหัส PIN
 * @return bool true ถ้าถูกต้อง
 */
function is_valid_pin(string $pin): bool
{
    return BAM_Bank_Validator::is_valid_pin($pin);
}

/**
 * ตรวจสอบเลขบัตรประชาชน (backward compatibility)
 * @param string $national_id เลขบัตรประชาชน
 * @return bool true ถ้าถูกต้อง
 */
function is_valid_national_id(string $national_id): bool
{
    return BAM_Bank_Validator::is_valid_national_id($national_id);
}

/**
 * ตรวจสอบเบอร์โทรศัพท์ (backward compatibility)
 * @param string $phone_number เบอร์โทรศัพท์
 * @return bool true ถ้าถูกต้อง
 */
function is_valid_phone_number(string $phone_number): bool
{
    return BAM_Bank_Validator::is_valid_phone_number($phone_number);
}

/**
 * ตรวจสอบสถานะ (backward compatibility)
 * @param string $status สถานะ
 * @return bool true ถ้าถูกต้อง
 */
function is_valid_status(string $status): bool
{
    return BAM_Bank_Validator::is_valid_status($status);
}

/**
 * ตรวจสอบ MIME type ของรูปภาพ (backward compatibility)
 * @param string $mime_type MIME type
 * @return bool true ถ้าถูกต้อง
 */
function is_valid_image_type(string $mime_type): bool
{
    return BAM_Bank_Validator::is_valid_image_type($mime_type);
}

/**
 * ตรวจสอบว่าเป็นรูปภาพจริง (backward compatibility)
 * @param string $file_path path ของไฟล์
 * @return bool true ถ้าเป็นรูปภาพจริง
 */
function is_real_image(string $file_path): bool
{
    return BAM_Bank_Validator::is_real_image($file_path);
}
?>