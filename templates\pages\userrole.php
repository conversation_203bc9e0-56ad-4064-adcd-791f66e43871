<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

/**
 * คลาสสำหรับจัดการ User Role Management
 * รวมการแสดงผลตารางและการจัดการข้อมูล user roles
 */
class BAM_UserRole_Manager
{
    // ค่าคงที่สำหรับการจัดการ user roles
    const TABLE_ID = 'display_userrole';
    const PERMISSION_KEY = 'can_access_userrole';
    const SEARCH_INPUT_ID = 'custom-search';

    /**
     * แสดงตารางข้อมูล user roles
     * @return string HTML ของตาราง user roles
     */
    public static function render_table()
    {
        ob_start();
        ?>
        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4 p-3">
                    <?php echo self::render_table_header(); ?>
                    <?php echo self::render_table_content(); ?>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * แสดงส่วนหัวของตาราง (ปุ่มเพิ่มและช่องค้นหา)
     * @return string HTML ของส่วนหัวตาราง
     */
    private static function render_table_header()
    {
        ob_start();
        ?>
        <div class="row">
            <div class="col-md-10 col-5">
                <?php echo self::render_add_button(); ?>
            </div>
            <div class="col-md-2 col-7">
                <?php echo self::render_search_input(); ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * แสดงปุ่มเพิ่ม user role (ปิดใช้งานในขณะนี้)
     * @return string HTML ของปุ่มเพิ่ม
     */
    private static function render_add_button()
    {
        // ปิดใช้งานการเพิ่ม user role ใหม่ในขณะนี้
        return '<!-- ปุ่มเพิ่ม user role ถูกปิดใช้งาน -->';

        // หากต้องการเปิดใช้งาน ให้ uncomment บรรทัดด้านล่าง
        /*
        return '
        <a href="/add-userrole" onclick="navigatePage(event, this.href);" class="btn btn-primary shadow-sm">
            <i class="bi bi-plus-lg"></i> เพิ่มหน้าที่
        </a>';
        */
    }

    /**
     * แสดงช่องค้นหา
     * @return string HTML ของช่องค้นหา
     */
    private static function render_search_input()
    {
        return sprintf(
            '<input type="text" id="%s" class="form-control" placeholder="ค้นหา">',
            esc_attr(self::SEARCH_INPUT_ID)
        );
    }

    /**
     * แสดงเนื้อหาตาราง
     * @return string HTML ของเนื้อหาตาราง
     */
    private static function render_table_content()
    {
        ob_start();
        ?>
        <div class="col-md-12 mt-2">
            <div class="table-responsive">
                <table id="<?php echo esc_attr(self::TABLE_ID); ?>" class="table table-bordered table-striped table-hover border rounded">
                    <?php echo self::render_table_head(); ?>
                    <tbody></tbody>
                </table>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * แสดงหัวตาราง
     * @return string HTML ของหัวตาราง
     */
    private static function render_table_head()
    {
        $headers = array(
            'ลำดับ',
            'Role Name',
            'การกระทำ'
        );

        $header_html = '<thead><tr>';
        foreach ($headers as $header) {
            $header_html .= '<th>' . esc_html($header) . '</th>';
        }
        $header_html .= '</tr></thead>';

        return $header_html;
    }

    /**
     * โหลดข้อมูล user roles จากฐานข้อมูล
     * ใช้สำหรับ AJAX request
     */
    public static function load_data()
    {
        // ตรวจสอบสิทธิ์และความปลอดภัย
        self::verify_ajax_security();

        // ดึงข้อมูลจากฐานข้อมูล
        $data = self::get_userrole_data();

        // ส่งข้อมูลกลับในรูปแบบ JSON
        wp_send_json_success($data);
    }

    /**
     * ตรวจสอบความปลอดภัยสำหรับ AJAX request
     */
    private static function verify_ajax_security()
    {
        // โหลด permission handler
        require_once get_template_directory() . '/includes/permission-handler.php';

        // ตรวจสอบสิทธิ์การเข้าถึงและ nonce
        verify_ajax_permission(self::PERMISSION_KEY);
    }

    /**
     * ดึงข้อมูล user roles จากฐานข้อมูล
     * @return array ข้อมูล user roles
     */
    private static function get_userrole_data()
    {
        global $wpdb;
        $table = $wpdb->prefix . 'userrole';

        // ใช้ prepared statement เพื่อความปลอดภัย
        $results = $wpdb->get_results(
            $wpdb->prepare("SELECT id, role_name, created_at FROM %i ORDER BY role_name ASC", $table),
            'ARRAY_A'
        );

        return $results ?: array();
    }
}

/**
 * ฟังก์ชันสำหรับแสดงตาราง user roles (shortcode)
 * @return string HTML ของตาราง user roles
 */
function display_userrole_table()
{
    return BAM_UserRole_Manager::render_table();
}
add_shortcode('display_userrole', 'display_userrole_table');

/**
 * ฟังก์ชันสำหรับโหลดข้อมูล user roles (AJAX handler)
 */
function bam_load_userrole_data()
{
    BAM_UserRole_Manager::load_data();
}
add_action('wp_ajax_nopriv_load_userrole_data', 'bam_load_userrole_data');
?>