.bg-cover {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    overflow: hidden;
}

.bg-overlay::after {
    content: "";
    position: absolute;
    inset: 0;
    /* background: rgba(0, 0, 0, 0.2); */
    z-index: 1;
}

.bg-overlay > * {
    position: relative;
    z-index: 2;
}

.brand-image-login {
    width: 1.5rem;
}

hr,#footer {
    display: none;
}

.error-alert {
  min-height: 48px;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.error-alert .alert {
  margin: 0;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
}