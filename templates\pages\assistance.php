<?php
if (!defined('ABSPATH')) exit; // ป้องกันเข้าตรงไฟล์โดยตรง

function display_assistance_table()
{
    ob_start();
?>
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4 p-3">
                <div class="row">
                    <div class="col-md-10 col-5">
                        <?php if (!empty($_SESSION['bam_permission']['can_access_add_assistance'])): ?>
                        <a href="/add-assistance" onclick="navigatePage(event, this.href);" class="btn btn-primary shadow-sm">
                            <i class="bi bi-plus-lg"></i> เพิ่มผู้ช่วย
                        </a>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-2 col-7">
                        <input type="text" id="custom-search" class="form-control" placeholder="ค้นหา">
                    </div>
                    <div class="col-md-12 mt-2">
                        <div class="table-responsive">
                            <table id="display_assistance" class="table table-bordered table-striped table-hover border rounded">
                                <thead>
                                    <tr>
                                        <th>ลำดับ</th>
                                        <th>ชื่อผู้ใช้</th>
                                        <th>สร้างเมื่อ</th>
                                        <th>เข้าระบบล่าสุด</th>
                                        <th>ดำเนินการทำ</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php
    return ob_get_clean();
}
add_shortcode('display_assistance', 'display_assistance_table');

// โหลดข้อมูล
function bam_load_assistance_data()
{
    require_once get_template_directory() . '/includes/permission-handler.php';
    verify_ajax_permission('can_access_assistance');

    global $wpdb;
    $table = $wpdb->prefix . 'assistance';

    $results = $wpdb->get_results("SELECT * FROM $table", ARRAY_A);
    wp_send_json_success($results);
}
add_action('wp_ajax_nopriv_load_assistance_data', 'bam_load_assistance_data');
?>